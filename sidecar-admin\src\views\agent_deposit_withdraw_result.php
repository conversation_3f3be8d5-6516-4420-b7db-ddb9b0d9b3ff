<?php
$title = 'Operation Result - Sidecar Admin';
ob_start();
?>

<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?= baseUrl() ?>">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="<?= baseUrl('agents/deposit-withdraw') ?>">Agent Deposit/Withdraw</a></li>
                <li class="breadcrumb-item active">Result</li>
            </ol>
        </nav>

        <h1 class="mb-4">
            <i class="bi bi-check-circle text-success"></i> Operation Completed
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="bi bi-check-circle"></i> Operation Successful
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <h6><i class="bi bi-info-circle"></i> Transaction Details</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Transaction ID:</strong><br>
                            <code><?= htmlspecialchars($operation['tx_id']) ?></code>
                        </div>
                        <div class="col-md-6">
                            <strong>Processed At:</strong><br>
                            <?= date('M j, Y g:i:s A', strtotime($operation['created_at'])) ?>
                        </div>
                    </div>
                </div>
                
                <h6 class="mb-3">Operation Summary</h6>
                <div class="row mb-4">
                    <div class="col-md-3">
                        <strong>Operation Type:</strong><br>
                        <?php
                        // Convert operation to display format
                        $operationType = $operation['direction'] ?? 'Unknown';
                        if (strpos($operationType, 'Deposit') !== false) {
                            $operationDisplay = '<i class="bi bi-plus-circle text-success"></i> ' .
                                (strpos($operationType, 'Cash') !== false ? 'Cash Deposit' : 'Wallet Deposit');
                        } elseif (strpos($operationType, 'Withdraw') !== false) {
                            $operationDisplay = '<i class="bi bi-dash-circle text-warning"></i> ' .
                                (strpos($operationType, 'Cash') !== false ? 'Cash Withdrawal' : 'Wallet Withdrawal');
                        } else {
                            // Legacy transfer format
                            $operationDisplay = str_replace(['To', 'Agent', 'Nestblock'], [' → ', 'Agent', 'Nestblock'], $operationType);
                        }
                        echo $operationDisplay;
                        ?>
                    </div>
                    <div class="col-md-3">
                        <strong>Amount:</strong><br>
                        <span class="h5 text-primary">
                            <?= formatCurrency($operation['send_amount'], $operation['send_curr']) ?>
                        </span>
                    </div>
                    <div class="col-md-3">
                        <strong>Agent ID:</strong><br>
                        #<?= htmlspecialchars($operation['agent_user_id']) ?>
                    </div>
                    <div class="col-md-3">
                        <strong>Platform Account:</strong><br>
                        <?= htmlspecialchars($operation['counterparty_type']) ?>
                    </div>
                </div>
                
                <h6 class="mb-3">Balance Changes</h6>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Agent Account</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Before:</span>
                                    <span class="balance-positive">
                                        <?= formatCurrency($operation['agent_balance_before'], $operation['send_curr']) ?>
                                    </span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Change:</span>
                                    <span class="<?= ($operation['agent_balance_after'] - $operation['agent_balance_before']) >= 0 ? 'text-success' : 'text-danger' ?>">
                                        <?php
                                        $change = $operation['agent_balance_after'] - $operation['agent_balance_before'];
                                        echo ($change >= 0 ? '+' : '') . formatCurrency($change, $operation['send_curr']);
                                        ?>
                                    </span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <strong>After:</strong>
                                    <strong class="<?= $operation['agent_balance_after'] >= 0 ? 'balance-positive' : 'balance-negative' ?>">
                                        <?= formatCurrency($operation['agent_balance_after'], $operation['send_curr']) ?>
                                    </strong>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><?= htmlspecialchars($operation['counterparty_type']) ?> Account</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Before:</span>
                                    <span class="balance-positive">
                                        <?= formatCurrency($operation['counterparty_balance_before'], $operation['send_curr']) ?>
                                    </span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Change:</span>
                                    <span class="<?= ($operation['counterparty_balance_after'] - $operation['counterparty_balance_before']) >= 0 ? 'text-success' : 'text-danger' ?>">
                                        <?php
                                        $change = $operation['counterparty_balance_after'] - $operation['counterparty_balance_before'];
                                        echo ($change >= 0 ? '+' : '') . formatCurrency($change, $operation['send_curr']);
                                        ?>
                                    </span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <strong>After:</strong>
                                    <strong class="<?= $operation['counterparty_balance_after'] >= 0 ? 'balance-positive' : 'balance-negative' ?>">
                                        <?= formatCurrency($operation['counterparty_balance_after'], $operation['send_curr']) ?>
                                    </strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h6 class="mb-3">Ledger Entries</h6>
                <div class="table-responsive mb-4">
                    <table class="table table-bordered table-sm">
                        <thead class="table-light">
                            <tr>
                                <th>Account</th>
                                <th>Direction</th>
                                <th>send_amount</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            // Determine posting based on direction
                            $postingRules = getPostingRules($operation['direction']);
                            ?>
                            <tr>
                                <td>
                                    <?= $postingRules['debit'] === 'Agent' ? 'Agent' : htmlspecialchars($operation['counterparty_type']) ?>
                                </td>
                                <td>
                                    <span class="badge bg-info">Debit</span>
                                </td>
                                <td><?= formatCurrency($operation['send_amount'], $operation['send_curr']) ?></td>
                                <td><?= htmlspecialchars($operation['direction']) ?></td>
                            </tr>
                            <tr>
                                <td>
                                    <?= $postingRules['credit'] === 'Agent' ? 'Agent' : htmlspecialchars($operation['counterparty_type']) ?>
                                </td>
                                <td>
                                    <span class="badge bg-warning">Credit</span>
                                </td>
                                <td><?= formatCurrency($operation['send_amount'], $operation['send_curr']) ?></td>
                                <td><?= htmlspecialchars($operation['direction']) ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <h6 class="mb-3">Transfer Details</h6>
                <div class="audit-entry">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Executed By:</strong><br>
                            <?= htmlspecialchars($operation['created_by']) ?>
                        </div>
                        <div class="col-md-6">
                            <strong>Timestamp:</strong><br>
                            <?= date('M j, Y g:i:s A', strtotime($operation['created_at'])) ?>
                        </div>
                    </div>
                    <div class="mt-3">
                        <strong>Reason:</strong><br>
                        <div class="bg-light p-3 rounded">
                            <?= nl2br(htmlspecialchars($operation['reason'])) ?>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4 d-flex justify-content-between">
                    <a href="<?= baseUrl('agents/deposit-withdraw') ?>" class="btn btn-outline-primary">
                        <i class="bi bi-plus-circle"></i> New Operation
                    </a>
                    <div>
                        <button type="button" class="btn btn-outline-secondary me-2" onclick="window.print()">
                            <i class="bi bi-printer"></i> Print
                        </button>
                        <a href="<?= baseUrl('reports/audits?tx_id=' . urlencode($operation['tx_id'])) ?>" class="btn btn-info">
                            <i class="bi bi-file-text"></i> View in Audit Log
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-info-circle"></i> What Happened?
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-3">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Balances Updated</strong><br>
                        <small class="text-muted">Both account balances have been updated in the vendor system</small>
                    </li>
                    <li class="mb-3">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Ledger Entries Created</strong><br>
                        <small class="text-muted">Double-entry bookkeeping records have been created</small>
                    </li>
                    <li class="mb-3">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Vendor Transactions</strong><br>
                        <small class="text-muted">Transaction records have been inserted into the vendor system</small>
                    </li>
                    <li class="mb-3">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Audit Trail</strong><br>
                        <small class="text-muted">Complete audit record has been created for compliance</small>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-shield-check"></i> Compliance Note
                </h6>
            </div>
            <div class="card-body">
                <p class="small text-muted mb-0">
                    This transfer has been recorded with transaction ID 
                    <code><?= htmlspecialchars($operation['tx_id']) ?></code> 
                    and is permanently stored in the audit trail. The transfer cannot be reversed 
                    through this system.
                </p>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-exclamation-triangle"></i> Important
                </h6>
            </div>
            <div class="card-body">
                <p class="small mb-0">
                    <strong>This transfer is final.</strong> If you need to reverse this transaction, 
                    you must create a new transfer in the opposite direction with appropriate documentation.
                </p>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .card-header, nav, .col-lg-4 {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .audit-entry {
        border-left: 3px solid #000 !important;
    }
}
</style>

<?php
$content = ob_get_clean();
include __DIR__ . '/layout.php';
?>
