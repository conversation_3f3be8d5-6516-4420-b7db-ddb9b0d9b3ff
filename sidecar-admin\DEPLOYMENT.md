# Sidecar Admin Deployment Guide

## Production Deployment

### Prerequisites

- **Server Requirements:**
  - Linux server (Ubuntu 20.04+ or CentOS 8+ recommended)
  - PHP 8.1+ with extensions: pdo, pdo_mysql, session, json, mbstring
  - MySQL/MariaDB 8.0+
  - Web server (Apache 2.4+ or Nginx 1.18+)
  - SSL certificate for HTTPS

- **Database Access:**
  - Existing vendor database with read/write access
  - Ability to create new tables (sidecar_* tables)
  - Database user with appropriate privileges

### Step 1: Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install PHP 8.1 and required extensions
sudo apt install php8.1 php8.1-fpm php8.1-mysql php8.1-mbstring php8.1-json php8.1-session

# Install web server (Apache example)
sudo apt install apache2

# Enable required Apache modules
sudo a2enmod rewrite ssl headers
```

### Step 2: Application Deployment

```bash
# Create application directory
sudo mkdir -p /var/www/sidecar-admin
cd /var/www/sidecar-admin

# Copy application files
# (Upload via SCP, rsync, or git clone)
sudo chown -R www-data:www-data /var/www/sidecar-admin
sudo chmod -R 755 /var/www/sidecar-admin
sudo chmod -R 775 /var/www/sidecar-admin/logs
sudo chmod +x /var/www/sidecar-admin/bin/reconcile.php
```

### Step 3: Environment Configuration

```bash
# Copy and configure environment file
sudo cp .env.example .env
sudo nano .env
```

**Production .env Configuration:**
```env
# Database Configuration
DB_HOST=your-db-host.com
DB_PORT=3306
DB_NAME=your_vendor_database
DB_USER=sidecar_ops
DB_PASS=secure_database_password

# Security
SESSION_SECRET=your-32-character-random-secret-key
APP_ENV=production

# Business Configuration
NESTBLOCK_CASH_USER_ID=1001
NESTBLOCK_WALLET_USER_ID=1002

# Feature Flags
SET_FINAL_BALANCE_IN_VENDOR_TRANSACTIONS=true

# Transfer Limits
MIN_TRANSFER_send_amount=1.00
MAX_TRANSFER_send_amount=50000.00

# Allowed Currencies
ALLOWED_CURRENCIES=USD,EUR,GBP

# Remittance Status Restrictions
DISALLOWED_REMITTANCE_STATUSES=PaidOut,Cancelled,Refunded,Failed
```

### Step 4: Database Setup

```bash
# Connect to your vendor database
mysql -h your-db-host.com -u root -p your_vendor_database

# Run sidecar schema installation
mysql -h your-db-host.com -u sidecar_ops -p your_vendor_database < sql/001_sidecar_schema.sql

# Create initial admin users
mysql -h your-db-host.com -u sidecar_ops -p your_vendor_database < sql/002_seed_users.sql
```

**Create Dedicated Database User:**
```sql
-- Create dedicated user for sidecar operations
CREATE USER 'sidecar_ops'@'%' IDENTIFIED BY 'secure_database_password';

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON your_vendor_database.users TO 'sidecar_ops'@'%';
GRANT SELECT, INSERT, UPDATE ON your_vendor_database.transactions TO 'sidecar_ops'@'%';
GRANT SELECT ON your_vendor_database.remittances TO 'sidecar_ops'@'%';
GRANT SELECT, INSERT, UPDATE ON your_vendor_database.beneficiaries TO 'sidecar_ops'@'%';

-- Full access to sidecar tables
GRANT ALL PRIVILEGES ON your_vendor_database.sidecar_* TO 'sidecar_ops'@'%';

FLUSH PRIVILEGES;
```

### Step 5: Web Server Configuration

**Apache Virtual Host (`/etc/apache2/sites-available/sidecar-admin.conf`):**
```apache
<VirtualHost *:443>
    ServerName sidecar-admin.yourdomain.com
    DocumentRoot /var/www/sidecar-admin/public
    
    # SSL Configuration
    SSLEngine on
    SSLCertificateFile /path/to/your/certificate.crt
    SSLCertificateKeyFile /path/to/your/private.key
    
    # Security Headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    
    # PHP Configuration
    <Directory /var/www/sidecar-admin/public>
        AllowOverride All
        Require all granted
        
        # Disable server signature
        ServerSignature Off
        
        # Hide PHP version
        Header unset X-Powered-By
    </Directory>
    
    # Deny access to sensitive directories
    <Directory /var/www/sidecar-admin/src>
        Require all denied
    </Directory>
    
    <Directory /var/www/sidecar-admin/logs>
        Require all denied
    </Directory>
    
    <Directory /var/www/sidecar-admin/sql>
        Require all denied
    </Directory>
    
    # Logging
    ErrorLog ${APACHE_LOG_DIR}/sidecar-admin_error.log
    CustomLog ${APACHE_LOG_DIR}/sidecar-admin_access.log combined
</VirtualHost>

# Redirect HTTP to HTTPS
<VirtualHost *:80>
    ServerName sidecar-admin.yourdomain.com
    Redirect permanent / https://sidecar-admin.yourdomain.com/
</VirtualHost>
```

**Enable Site:**
```bash
sudo a2ensite sidecar-admin.conf
sudo systemctl reload apache2
```

### Step 6: PHP Configuration

**Production PHP Settings (`/etc/php/8.1/fpm/php.ini`):**
```ini
# Security
expose_php = Off
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/log/php/error.log

# Performance
memory_limit = 256M
max_execution_time = 60
max_input_time = 60
post_max_size = 10M
upload_max_filesize = 10M

# Session Security
session.cookie_secure = 1
session.cookie_httponly = 1
session.cookie_samesite = "Strict"
session.use_strict_mode = 1
session.gc_maxlifetime = 3600
```

### Step 7: Cron Jobs

```bash
# Edit crontab for www-data user
sudo crontab -u www-data -e

# Add reconciliation job (runs at 2 AM daily)
0 2 * * * /var/www/sidecar-admin/bin/reconcile.php >> /var/www/sidecar-admin/logs/cron.log 2>&1

# Add log cleanup job (runs weekly)
0 3 * * 0 find /var/www/sidecar-admin/logs -name "*.log" -mtime +30 -delete

# Add session cleanup job (runs hourly)
0 * * * * find /var/www/sidecar-admin/logs -name "sess_*" -mtime +1 -delete
```

### Step 8: Security Hardening

```bash
# Set proper file permissions
sudo chown -R www-data:www-data /var/www/sidecar-admin
sudo find /var/www/sidecar-admin -type f -exec chmod 644 {} \;
sudo find /var/www/sidecar-admin -type d -exec chmod 755 {} \;
sudo chmod 775 /var/www/sidecar-admin/logs
sudo chmod +x /var/www/sidecar-admin/bin/reconcile.php

# Secure sensitive files
sudo chmod 600 /var/www/sidecar-admin/.env
sudo chown www-data:www-data /var/www/sidecar-admin/.env

# Configure firewall
sudo ufw allow 22/tcp   # SSH
sudo ufw allow 80/tcp   # HTTP (for redirect)
sudo ufw allow 443/tcp  # HTTPS
sudo ufw enable
```

### Step 9: Monitoring & Logging

**Log Rotation (`/etc/logrotate.d/sidecar-admin`):**
```
/var/www/sidecar-admin/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload apache2
    endscript
}
```

**Health Check Script (`/var/www/sidecar-admin/bin/health-check.sh`):**
```bash
#!/bin/bash
# Simple health check for monitoring

# Check if web server is responding
if curl -f -s https://sidecar-admin.yourdomain.com/health > /dev/null; then
    echo "OK: Web server responding"
else
    echo "ERROR: Web server not responding"
    exit 1
fi

# Check database connectivity
if php -r "
    require_once '/var/www/sidecar-admin/src/bootstrap.php';
    try {
        \$pdo->query('SELECT 1');
        echo 'OK: Database connected\n';
    } catch (Exception \$e) {
        echo 'ERROR: Database connection failed\n';
        exit(1);
    }
"; then
    echo "Database check passed"
else
    echo "Database check failed"
    exit 1
fi

echo "All health checks passed"
```

### Step 10: Initial Setup & Testing

```bash
# Test database connection
php -r "
require_once '/var/www/sidecar-admin/src/bootstrap.php';
echo 'Database connection: ' . (\$pdo ? 'OK' : 'FAILED') . PHP_EOL;
"

# Run initial reconciliation
sudo -u www-data /var/www/sidecar-admin/bin/reconcile.php

# Test web access
curl -I https://sidecar-admin.yourdomain.com/

# Change default admin password
# Login to web interface and change <NAME_EMAIL>
```

## Post-Deployment Checklist

- [ ] **Security**
  - [ ] SSL certificate installed and working
  - [ ] Default admin password changed
  - [ ] Firewall configured
  - [ ] File permissions set correctly
  - [ ] Sensitive directories blocked by web server

- [ ] **Functionality**
  - [ ] Database connection working
  - [ ] Login system functional
  - [ ] Beneficiary amendment working
  - [ ] Agent transfers working
  - [ ] Audit trail accessible
  - [ ] Reconciliation job running

- [ ] **Monitoring**
  - [ ] Log rotation configured
  - [ ] Cron jobs scheduled
  - [ ] Health check script working
  - [ ] Error logging enabled

- [ ] **Performance**
  - [ ] PHP settings optimized
  - [ ] Database indexes created
  - [ ] Web server caching configured

## Backup Strategy

```bash
# Database backup script
#!/bin/bash
BACKUP_DIR="/var/backups/sidecar-admin"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup sidecar tables only
mysqldump -h your-db-host.com -u sidecar_ops -p \
  --single-transaction \
  --routines \
  --triggers \
  --where="1 limit 1000000" \
  your_vendor_database \
  $(mysql -h your-db-host.com -u sidecar_ops -p -e "SHOW TABLES LIKE 'sidecar_%'" your_vendor_database | grep -v Tables_in) \
  > $BACKUP_DIR/sidecar_backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/sidecar_backup_$DATE.sql

# Remove backups older than 30 days
find $BACKUP_DIR -name "sidecar_backup_*.sql.gz" -mtime +30 -delete

echo "Backup completed: sidecar_backup_$DATE.sql.gz"
```

## Troubleshooting

### Common Issues

**500 Internal Server Error**
- Check Apache error logs: `sudo tail -f /var/log/apache2/sidecar-admin_error.log`
- Verify file permissions
- Check PHP error logs

**Database Connection Failed**
- Verify database credentials in `.env`
- Test connection manually: `mysql -h host -u user -p database`
- Check firewall rules

**Session Issues**
- Verify `SESSION_SECRET` is set
- Check session directory permissions
- Clear browser cookies

**Reconciliation Failures**
- Check cron logs: `sudo tail -f /var/www/sidecar-admin/logs/cron.log`
- Run manually: `sudo -u www-data /var/www/sidecar-admin/bin/reconcile.php`
- Verify `NESTBLOCK_*_USER_ID` values

For additional support, check application logs in `/var/www/sidecar-admin/logs/`.
