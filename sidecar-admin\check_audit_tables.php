<?php
require_once 'src/bootstrap.php';

echo "Checking audit tables and their schemas...\n\n";

try {
    // Get all tables
    $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%audit%'")->fetchAll(PDO::FETCH_COLUMN);
    
    echo "Found audit tables:\n";
    foreach ($tables as $table) {
        echo "- $table\n";
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    
    // Check each audit table schema
    foreach ($tables as $table) {
        echo "\nTable: $table\n";
        echo str_repeat("-", 30) . "\n";
        
        $columns = $pdo->query("PRAGMA table_info($table)")->fetchAll(PDO::FETCH_ASSOC);
        foreach ($columns as $col) {
            echo "- {$col['name']} ({$col['type']})\n";
        }
        
        // Check if table has data
        $count = $pdo->query("SELECT COUNT(*) as count FROM $table")->fetch()['count'];
        echo "Records: $count\n";
        
        if ($count > 0) {
            echo "Sample record:\n";
            $sample = $pdo->query("SELECT * FROM $table LIMIT 1")->fetch(PDO::FETCH_ASSOC);
            foreach ($sample as $key => $value) {
                $displayValue = is_string($value) ? substr($value, 0, 50) : $value;
                echo "  $key: $displayValue\n";
            }
        }
    }
    
    // Also check if sidecar_vendor_sync_audits table exists
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "Checking for sidecar_vendor_sync_audits table...\n";
    
    $vendorSyncExists = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='sidecar_vendor_sync_audits'")->fetch();
    
    if ($vendorSyncExists) {
        echo "✅ sidecar_vendor_sync_audits table exists\n";
        $vendorColumns = $pdo->query("PRAGMA table_info(sidecar_vendor_sync_audits)")->fetchAll(PDO::FETCH_ASSOC);
        foreach ($vendorColumns as $col) {
            echo "- {$col['name']} ({$col['type']})\n";
        }
    } else {
        echo "❌ sidecar_vendor_sync_audits table does NOT exist\n";
        echo "This explains the SQL error in the reports!\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
