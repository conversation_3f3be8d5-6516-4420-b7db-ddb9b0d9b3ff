.PHONY: help build up down logs shell test clean install seed

help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

build: ## Build Docker containers
	docker-compose build

up: ## Start all services
	docker-compose up -d

down: ## Stop all services
	docker-compose down

logs: ## Show logs from all services
	docker-compose logs -f

shell: ## Access the sidecar-admin container shell
	docker-compose exec sidecar-admin bash

test: ## Run PHPUnit tests
	docker-compose exec sidecar-admin php vendor/bin/phpunit

clean: ## Clean up containers and volumes
	docker-compose down -v
	docker system prune -f

install: ## Install dependencies and setup
	cp .env.example .env
	docker-compose build
	docker-compose up -d
	@echo "Waiting for MySQL to be ready..."
	sleep 10
	make seed

seed: ## Run database migrations and seed data
	docker-compose exec mysql mysql -u root -prootpassword vendor_db < /docker-entrypoint-initdb.d/001_sidecar_schema.sql
	docker-compose exec mysql mysql -u root -prootpassword vendor_db < /docker-entrypoint-initdb.d/002_seed_users.sql

reconcile: ## Run reconciliation job
	docker-compose exec sidecar-admin php bin/reconcile.php

dev: ## Start development environment
	make install
	@echo "Development environment ready!"
	@echo "Sidecar Admin: http://localhost:8080"
	@echo "phpMyAdmin: http://localhost:8081"
