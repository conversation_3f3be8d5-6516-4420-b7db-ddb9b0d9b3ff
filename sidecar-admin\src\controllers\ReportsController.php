<?php
/**
 * Reports Controller
 * Handles audit reports and reconciliation
 */

class ReportsController {
    
    /**
     * Show audit trail
     */
    public function audits($params = []) {
        requireRole(['Admin', 'Finance', 'Compliance', 'Viewer']);
        
        global $pdo;
        
        try {
            // Get filter parameters
            $txId = $_GET['tx_id'] ?? '';
            $type = $_GET['type'] ?? '';
            $dateFrom = $_GET['date_from'] ?? '';
            $dateTo = $_GET['date_to'] ?? '';
            $page = max(1, (int)($_GET['page'] ?? 1));
            $limit = 20;
            $offset = ($page - 1) * $limit;
            
            // Build query conditions
            $conditions = [];
            $params = [];
            
            if ($txId) {
                $conditions[] = "tx_id = ?";
                $params[] = $txId;
            }
            
            if ($type) {
                $conditions[] = "type = ?";
                $params[] = $type;
            }
            
            if ($dateFrom) {
                $conditions[] = "created_at >= ?";
                $params[] = $dateFrom . ' 00:00:00';
            }
            
            if ($dateTo) {
                $conditions[] = "created_at <= ?";
                $params[] = $dateTo . ' 23:59:59';
            }
            
            $whereClause = $conditions ? 'WHERE ' . implode(' AND ', $conditions) : '';
            
            // Check which audit tables exist
            $driver = $pdo->getAttribute(PDO::ATTR_DRIVER_NAME);
            $beneficiaryTableExists = false;
            $vendorSyncTableExists = false;

            if ($driver === 'mysql') {
                $beneficiaryTableExists = $pdo->query("SHOW TABLES LIKE 'sidecar_beneficiary_amend_audits'")->fetch() !== false;
                $vendorSyncTableExists = $pdo->query("SHOW TABLES LIKE 'sidecar_vendor_sync_audits'")->fetch() !== false;
            } else {
                // SQLite or other databases
                $beneficiaryTableExists = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='sidecar_beneficiary_amend_audits'")->fetch() !== false;
                $vendorSyncTableExists = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='sidecar_vendor_sync_audits'")->fetch() !== false;
            }

            // Build query based on available tables
            $queries = [];

            if ($beneficiaryTableExists) {
                if ($driver === 'mysql') {
                    $queries[] = "
                        SELECT 'amendment' as type, tx_id, created_at,
                               CONCAT('Beneficiary amended for invoice ', COALESCE(invoice, 'N/A')) as description,
                               (SELECT email FROM sidecar_users WHERE id = changed_by_user_id) as user_email,
                               JSON_OBJECT('type', 'amendment', 'invoice', COALESCE(invoice, ''),
                                          'old_name', COALESCE(old_name, ''), 'new_name', COALESCE(new_name, ''),
                                          'old_email', COALESCE(old_email, ''), 'new_email', COALESCE(new_email, ''),
                                          'old_phone', COALESCE(old_phone, ''), 'new_phone', COALESCE(new_phone, ''),
                                          'reason', COALESCE(reason, '')) as details
                        FROM sidecar_beneficiary_amend_audits";
                } else {
                    $queries[] = "
                        SELECT 'amendment' as type, tx_id, created_at,
                               'Beneficiary amended for invoice ' || COALESCE(invoice, 'N/A') as description,
                               (SELECT email FROM sidecar_users WHERE id = changed_by_user_id) as user_email,
                               '{\"type\":\"amendment\",\"invoice\":\"' || COALESCE(invoice, '') || '\"}' as details
                        FROM sidecar_beneficiary_amend_audits";
                }
            }

            if ($vendorSyncTableExists) {
                if ($driver === 'mysql') {
                    $queries[] = "
                        SELECT 'transfer' as type, tx_id, created_at,
                               CONCAT('Transfer: ', counterparty_type, ' ↔ Agent (', currency, ' ', ABS(agent_delta), ')') as description,
                               (SELECT email FROM sidecar_users WHERE id = created_by_user_id) as user_email,
                               JSON_OBJECT('type', 'transfer', 'counterparty_type', counterparty_type, 'currency', currency) as details
                        FROM sidecar_vendor_sync_audits";
                } else {
                    $queries[] = "
                        SELECT 'transfer' as type, tx_id, created_at,
                               'Transfer: ' || counterparty_type || ' ↔ Agent (' || currency || ' ' || ABS(agent_delta) || ')' as description,
                               (SELECT email FROM sidecar_users WHERE id = created_by_user_id) as user_email,
                               '{\"type\":\"transfer\"}' as details
                        FROM sidecar_vendor_sync_audits";
                }
            }

            if (empty($queries)) {
                // No audit tables exist, return empty result
                $audits = [];
                $totalCount = 0;
            } else {
                $auditSql = implode(' UNION ALL ', $queries) . "
                    $whereClause
                    ORDER BY created_at DESC
                    LIMIT $limit OFFSET $offset
                ";

                $audits = fetchAll($pdo, $auditSql, $params);

                // Build count query based on available tables
                $countQueries = [];
                if ($beneficiaryTableExists) {
                    $countQueries[] = "SELECT tx_id, created_at, 'amendment' as type FROM sidecar_beneficiary_amend_audits";
                }
                if ($vendorSyncTableExists) {
                    $countQueries[] = "SELECT tx_id, created_at, 'transfer' as type FROM sidecar_vendor_sync_audits";
                }

                if (!empty($countQueries)) {
                    $countSql = "
                        SELECT COUNT(*) as total FROM (
                            " . implode(' UNION ALL ', $countQueries) . "
                        ) combined $whereClause
                    ";
                    $totalCount = fetchOne($pdo, $countSql, $params)['total'] ?? 0;
                } else {
                    $totalCount = 0;
                }
            }
            $totalPages = ceil($totalCount / $limit);
            
            // Parse JSON details
            foreach ($audits as &$audit) {
                $audit['details'] = json_decode($audit['details'], true);
            }
            
            include __DIR__ . '/../views/audits_list.php';
            
        } catch (Exception $e) {
            logMessage('error', 'Audits list error: ' . $e->getMessage());
            setFlashMessage('error', 'Unable to load audit records');
            redirect(baseUrl());
        }
    }
    
    /**
     * Show statements and reconciliation
     */
    public function statements($params = []) {
        requireRole(['Admin', 'Finance', 'Compliance', 'Viewer']);
        
        global $pdo, $config;
        
        try {
            // Get reconciliation data
            $reconciliation = $this->performReconciliation($pdo, $config);
            
            // Get recent transfers summary
            $transfersSummary = fetchAll($pdo, "
                SELECT 
                    counterparty_type,
                    currency,
                    COUNT(*) as transfer_count,
                    SUM(ABS(agent_delta)) as total_amount,
                    DATE(created_at) as transfer_date
                FROM sidecar_vendor_sync_audits 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY counterparty_type, currency, DATE(created_at)
                ORDER BY transfer_date DESC, counterparty_type
            ");
            
            // Get account balances over time (last 30 days)
            $balanceHistory = fetchAll($pdo, "
                SELECT 
                    DATE(created_at) as date,
                    counterparty_type,
                    AVG(agent_after) as avg_agent_balance,
                    AVG(counterparty_after) as avg_counterparty_balance
                FROM sidecar_vendor_sync_audits 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY DATE(created_at), counterparty_type
                ORDER BY date DESC
            ");
            
            include __DIR__ . '/../views/statements.php';
            
        } catch (Exception $e) {
            logMessage('error', 'Statements error: ' . $e->getMessage());
            setFlashMessage('error', 'Unable to load statements');
            redirect(baseUrl());
        }
    }
    
    /**
     * Perform reconciliation check
     */
    public function performReconciliation($pdo, $config) {
        $results = [
            'ledger_balance' => true,
            'agent_balances' => [],
            'system_balances' => [],
            'discrepancies' => [],
            'summary' => [
                'total_checks' => 0,
                'passed_checks' => 0,
                'failed_checks' => 0
            ]
        ];
        
        try {
            // 1. Check ledger balance (debits = credits for each tx_id)
            $ledgerCheck = fetchAll($pdo, "
                SELECT 
                    tx_id,
                    SUM(CASE WHEN direction = 'Debit' THEN amount ELSE 0 END) as total_debits,
                    SUM(CASE WHEN direction = 'Credit' THEN amount ELSE 0 END) as total_credits,
                    COUNT(*) as entry_count
                FROM sidecar_ledger_entries 
                GROUP BY tx_id
                HAVING total_debits != total_credits OR entry_count != 2
            ");
            
            $results['summary']['total_checks']++;
            if (empty($ledgerCheck)) {
                $results['summary']['passed_checks']++;
            } else {
                $results['summary']['failed_checks']++;
                $results['ledger_balance'] = false;
                foreach ($ledgerCheck as $issue) {
                    $results['discrepancies'][] = [
                        'type' => 'ledger_imbalance',
                        'tx_id' => $issue['tx_id'],
                        'description' => "Ledger imbalance: Debits={$issue['total_debits']}, Credits={$issue['total_credits']}, Entries={$issue['entry_count']}"
                    ];
                }
            }
            
            // 2. Check agent balances
            $agents = fetchAll($pdo, "
                SELECT DISTINCT agent_user_id 
                FROM sidecar_vendor_sync_audits
            ");
            
            foreach ($agents as $agent) {
                $agentId = $agent['agent_user_id'];
                $results['summary']['total_checks']++;
                
                // Calculate sidecar balance for agent
                $sidecarBalance = $this->calculateSidecarBalance($pdo, $agentId, 'Agent');
                
                // Get vendor balance
                $vendorBalance = fetchOne($pdo, "
                    SELECT balance FROM users WHERE id = ?
                ", [$agentId]);
                
                if ($vendorBalance) {
                    $vendorBalance = (float)$vendorBalance['balance'];
                    $diff = abs($sidecarBalance - $vendorBalance);
                    
                    if ($diff < 0.01) { // Allow small floating point differences
                        $results['summary']['passed_checks']++;
                        $results['agent_balances'][$agentId] = [
                            'sidecar' => $sidecarBalance,
                            'vendor' => $vendorBalance,
                            'status' => 'match'
                        ];
                    } else {
                        $results['summary']['failed_checks']++;
                        $results['agent_balances'][$agentId] = [
                            'sidecar' => $sidecarBalance,
                            'vendor' => $vendorBalance,
                            'status' => 'mismatch',
                            'difference' => $sidecarBalance - $vendorBalance
                        ];
                        $results['discrepancies'][] = [
                            'type' => 'agent_balance_mismatch',
                            'agent_id' => $agentId,
                            'description' => "Agent $agentId balance mismatch: Sidecar=$sidecarBalance, Vendor=$vendorBalance"
                        ];
                    }
                }
            }
            
            // 3. Check system account balances
            $systemAccounts = [
                'NestblockCash' => $config['business']['nestblock_cash_user_id'],
                'NestblockWallet' => $config['business']['nestblock_wallet_user_id']
            ];
            
            foreach ($systemAccounts as $type => $userId) {
                $results['summary']['total_checks']++;
                
                // Calculate sidecar balance
                $sidecarBalance = $this->calculateSidecarBalance($pdo, $userId, $type);
                
                // Get vendor balance
                $vendorBalance = fetchOne($pdo, "
                    SELECT balance FROM users WHERE id = ?
                ", [$userId]);
                
                if ($vendorBalance) {
                    $vendorBalance = (float)$vendorBalance['balance'];
                    $diff = abs($sidecarBalance - $vendorBalance);
                    
                    if ($diff < 0.01) {
                        $results['summary']['passed_checks']++;
                        $results['system_balances'][$type] = [
                            'sidecar' => $sidecarBalance,
                            'vendor' => $vendorBalance,
                            'status' => 'match'
                        ];
                    } else {
                        $results['summary']['failed_checks']++;
                        $results['system_balances'][$type] = [
                            'sidecar' => $sidecarBalance,
                            'vendor' => $vendorBalance,
                            'status' => 'mismatch',
                            'difference' => $sidecarBalance - $vendorBalance
                        ];
                        $results['discrepancies'][] = [
                            'type' => 'system_balance_mismatch',
                            'account_type' => $type,
                            'description' => "$type balance mismatch: Sidecar=$sidecarBalance, Vendor=$vendorBalance"
                        ];
                    }
                }
            }
            
        } catch (Exception $e) {
            logMessage('error', 'Reconciliation error: ' . $e->getMessage());
            $results['error'] = $e->getMessage();
        }
        
        return $results;
    }
    
    /**
     * Calculate sidecar balance for an account
     */
    private function calculateSidecarBalance($pdo, $ownerUserId, $accountType, $send_curr = 'USD') {
        // Get account ID
        $account = fetchOne($pdo, "
            SELECT id FROM sidecar_accounts 
            WHERE owner_user_id = ? AND account_type = ? AND currency = ?
        ", [$ownerUserId, $accountType, $send_curr]);
        
        if (!$account) {
            return 0.0; // No sidecar account means no sidecar activity
        }
        
        // Calculate balance from ledger entries
        $balance = fetchOne($pdo, "
            SELECT 
                SUM(CASE WHEN direction = 'Debit' THEN amount ELSE -amount END) as balance
            FROM sidecar_ledger_entries 
            WHERE account_id = ?
        ", [$account['id']]);
        
        return (float)($balance['balance'] ?? 0);
    }
}
