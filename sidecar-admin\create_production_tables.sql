-- Create missing audit tables for production MySQL database
-- Run this script on your production database to fix the audit reports

-- Create sidecar_vendor_sync_audits table if it doesn't exist
CREATE TABLE IF NOT EXISTS sidecar_vendor_sync_audits (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  tx_id CHAR(36) NOT NULL,
  agent_user_id BIGINT UNSIGNED NOT NULL,
  counterparty_user_id BIGINT UNSIGNED NOT NULL,
  counterparty_type ENUM('NestblockCash','NestblockWallet') NOT NULL,
  send_curr CHAR(3) NOT NULL DEFAULT 'USD',
  agent_delta DECIMAL(18,2) NOT NULL,         -- + increases agent, - decreases
  counterparty_delta DECIMAL(18,2) NOT NULL,  -- the opposite
  agent_before DECIMAL(18,2) NOT NULL,
  agent_after DECIMAL(18,2) NOT NULL,
  counterparty_before DECIMAL(18,2) NOT NULL,
  counterparty_after DECIMAL(18,2) NOT NULL,
  created_by_user_id BIGINT UNSIGNED NOT NULL,
  reason TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uniq_sync_tx (tx_id),
  INDEX idx_agent (agent_user_id),
  INDEX idx_counterparty (counterparty_user_id),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (created_by_user_id) REFERENCES sidecar_users(id)
);

-- Ensure sidecar_beneficiary_amend_audits table has correct structure
-- Check if the table exists and has the right columns
CREATE TABLE IF NOT EXISTS sidecar_beneficiary_amend_audits (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  tx_id CHAR(36) NOT NULL,
  invoice VARCHAR(100),
  remittance_id BIGINT UNSIGNED,
  beneficiary_id BIGINT UNSIGNED,
  old_name VARCHAR(255) NOT NULL,
  new_name VARCHAR(255) NOT NULL,
  old_email VARCHAR(255),
  new_email VARCHAR(255),
  old_phone VARCHAR(50) NOT NULL,
  new_phone VARCHAR(50) NOT NULL,
  reason TEXT NOT NULL,
  changed_by_user_id BIGINT UNSIGNED NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uniq_amend_tx (tx_id),
  INDEX idx_invoice (invoice),
  INDEX idx_remittance (remittance_id),
  INDEX idx_beneficiary (beneficiary_id),
  INDEX idx_changed_by (changed_by_user_id),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (changed_by_user_id) REFERENCES sidecar_users(id)
);

-- Add missing columns to existing beneficiary audit table if needed
-- These will fail silently if columns already exist
ALTER TABLE sidecar_beneficiary_amend_audits 
ADD COLUMN invoice VARCHAR(100) AFTER tx_id;

ALTER TABLE sidecar_beneficiary_amend_audits 
ADD COLUMN old_email VARCHAR(255) AFTER new_name;

ALTER TABLE sidecar_beneficiary_amend_audits 
ADD COLUMN new_email VARCHAR(255) AFTER old_email;

-- Add indexes for new columns
ALTER TABLE sidecar_beneficiary_amend_audits 
ADD INDEX idx_invoice (invoice);

-- Show table status
SELECT 'sidecar_beneficiary_amend_audits' as table_name, COUNT(*) as record_count 
FROM sidecar_beneficiary_amend_audits
UNION ALL
SELECT 'sidecar_vendor_sync_audits' as table_name, COUNT(*) as record_count 
FROM sidecar_vendor_sync_audits;
