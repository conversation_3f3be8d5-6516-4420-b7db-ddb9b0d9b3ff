<?php
$title = 'Statements & Reconciliation - Sidecar Admin';
ob_start();
?>

<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?= baseUrl() ?>">Dashboard</a></li>
                <li class="breadcrumb-item active">Statements</li>
            </ol>
        </nav>
        
        <h1 class="mb-4">
            <i class="bi bi-file-text"></i> Statements & Reconciliation
        </h1>
    </div>
</div>

<!-- Reconciliation Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-shield-check"></i> Reconciliation Status
                </h5>
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="runReconciliation()">
                    <i class="bi bi-arrow-clockwise"></i> Run Check
                </button>
            </div>
            <div class="card-body">
                <?php if (isset($reconciliation['error'])): ?>
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>Reconciliation Error:</strong> <?= htmlspecialchars($reconciliation['error']) ?>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h2 mb-0 <?= $reconciliation['summary']['failed_checks'] === 0 ? 'text-success' : 'text-danger' ?>">
                                    <?= $reconciliation['summary']['passed_checks'] ?>/<?= $reconciliation['summary']['total_checks'] ?>
                                </div>
                                <small class="text-muted">Checks Passed</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h2 mb-0 <?= $reconciliation['ledger_balance'] ? 'text-success' : 'text-danger' ?>">
                                    <i class="bi bi-<?= $reconciliation['ledger_balance'] ? 'check-circle' : 'x-circle' ?>"></i>
                                </div>
                                <small class="text-muted">Ledger Balance</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h2 mb-0 <?= empty(array_filter($reconciliation['agent_balances'], fn($b) => $b['status'] === 'mismatch')) ? 'text-success' : 'text-danger' ?>">
                                    <?= count(array_filter($reconciliation['agent_balances'], fn($b) => $b['status'] === 'match')) ?>
                                </div>
                                <small class="text-muted">Agent Balances OK</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h2 mb-0 <?= empty(array_filter($reconciliation['system_balances'], fn($b) => $b['status'] === 'mismatch')) ? 'text-success' : 'text-danger' ?>">
                                    <?= count(array_filter($reconciliation['system_balances'], fn($b) => $b['status'] === 'match')) ?>
                                </div>
                                <small class="text-muted">System Balances OK</small>
                            </div>
                        </div>
                    </div>
                    
                    <?php if (!empty($reconciliation['discrepancies'])): ?>
                        <div class="mt-4">
                            <h6 class="text-danger">
                                <i class="bi bi-exclamation-triangle"></i> Discrepancies Found
                            </h6>
                            <div class="list-group">
                                <?php foreach ($reconciliation['discrepancies'] as $discrepancy): ?>
                                    <div class="list-group-item list-group-item-danger">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <strong><?= ucfirst(str_replace('_', ' ', $discrepancy['type'])) ?></strong><br>
                                                <small><?= htmlspecialchars($discrepancy['description']) ?></small>
                                            </div>
                                            <?php if (isset($discrepancy['tx_id'])): ?>
                                                <small class="text-muted">
                                                    TX: <?= htmlspecialchars($discrepancy['tx_id']) ?>
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Balance Summary -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-people"></i> Agent Balances
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($reconciliation['agent_balances'])): ?>
                    <div class="text-center text-muted">
                        <i class="bi bi-inbox"></i>
                        <p class="mt-2">No agent balances to display</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Agent ID</th>
                                    <th>Sidecar</th>
                                    <th>Vendor</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($reconciliation['agent_balances'] as $agentId => $balance): ?>
                                    <tr>
                                        <td>#<?= $agentId ?></td>
                                        <td><?= formatCurrency($balance['sidecar']) ?></td>
                                        <td><?= formatCurrency($balance['vendor']) ?></td>
                                        <td>
                                            <?php if ($balance['status'] === 'match'): ?>
                                                <span class="badge bg-success">
                                                    <i class="bi bi-check"></i> Match
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">
                                                    <i class="bi bi-x"></i> Mismatch
                                                </span>
                                                <br><small class="text-danger">
                                                    Diff: <?= formatCurrency($balance['difference']) ?>
                                                </small>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-building"></i> System Account Balances
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($reconciliation['system_balances'])): ?>
                    <div class="text-center text-muted">
                        <i class="bi bi-inbox"></i>
                        <p class="mt-2">No system balances to display</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Account</th>
                                    <th>Sidecar</th>
                                    <th>Vendor</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($reconciliation['system_balances'] as $accountType => $balance): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($accountType) ?></td>
                                        <td><?= formatCurrency($balance['sidecar']) ?></td>
                                        <td><?= formatCurrency($balance['vendor']) ?></td>
                                        <td>
                                            <?php if ($balance['status'] === 'match'): ?>
                                                <span class="badge bg-success">
                                                    <i class="bi bi-check"></i> Match
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">
                                                    <i class="bi bi-x"></i> Mismatch
                                                </span>
                                                <br><small class="text-danger">
                                                    Diff: <?= formatCurrency($balance['difference']) ?>
                                                </small>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Summary -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-bar-chart"></i> Transfer Summary (Last 30 Days)
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($transfersSummary)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-inbox"></i>
                        <p class="mt-2">No transfers in the last 30 days</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Date</th>
                                    <th>Counterparty</th>
                                    <th>Currency</th>
                                    <th>Count</th>
                                    <th>Total send_amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($transfersSummary as $summary): ?>
                                    <tr>
                                        <td><?= date('M j, Y', strtotime($summary['transfer_date'])) ?></td>
                                        <td>
                                            <span class="badge bg-<?= $summary['counterparty_type'] === 'NestblockCash' ? 'info' : 'warning' ?>">
                                                <?= htmlspecialchars($summary['counterparty_type']) ?>
                                            </span>
                                        </td>
                                        <td><?= htmlspecialchars($summary['send_curr']) ?></td>
                                        <td>
                                            <span class="badge bg-secondary"><?= number_format($summary['transfer_count']) ?></span>
                                        </td>
                                        <td class="text-end">
                                            <?= formatCurrency($summary['total_send_amount'], $summary['send_curr']) ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Balance History Chart -->
<?php if (!empty($balanceHistory)): ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-graph-up"></i> Balance History (Last 30 Days)
                </h6>
            </div>
            <div class="card-body">
                <canvas id="balanceChart" height="100"></canvas>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php
$additionalJs = '
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
function runReconciliation() {
    // Show loading state
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.disabled = true;
    btn.innerHTML = \'<span class="spinner-border spinner-border-sm"></span> Running...\';
    
    // Reload page to trigger reconciliation
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

// Balance History Chart
' . (!empty($balanceHistory) ? '
const balanceData = ' . json_encode($balanceHistory) . ';

// Process data for chart
const dates = [...new Set(balanceData.map(d => d.date))].sort().reverse().slice(0, 30);
const cashData = [];
const walletData = [];

dates.forEach(date => {
    const cashEntry = balanceData.find(d => d.date === date && d.counterparty_type === "NestblockCash");
    const walletEntry = balanceData.find(d => d.date === date && d.counterparty_type === "NestblockWallet");
    
    cashData.push(cashEntry ? parseFloat(cashEntry.avg_counterparty_balance) : null);
    walletData.push(walletEntry ? parseFloat(walletEntry.avg_counterparty_balance) : null);
});

const ctx = document.getElementById("balanceChart").getContext("2d");
new Chart(ctx, {
    type: "line",
    data: {
        labels: dates.map(d => new Date(d).toLocaleDateString()),
        datasets: [
            {
                label: "NestblockCash",
                data: cashData,
                borderColor: "#0dcaf0",
                backgroundColor: "rgba(13, 202, 240, 0.1)",
                tension: 0.4
            },
            {
                label: "NestblockWallet",
                data: walletData,
                borderColor: "#ffc107",
                backgroundColor: "rgba(255, 193, 7, 0.1)",
                tension: 0.4
            }
        ]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return new Intl.NumberFormat("en-US", {
                            style: "send_curr",
                            send_curr: "USD"
                        }).format(value);
                    }
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.dataset.label + ": " + 
                               new Intl.NumberFormat("en-US", {
                                   style: "send_curr",
                                   send_curr: "USD"
                               }).format(context.parsed.y);
                    }
                }
            }
        }
    }
});
' : '') . '
</script>
';

$content = ob_get_clean();
include __DIR__ . '/layout.php';
?>
