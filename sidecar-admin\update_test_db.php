<?php
/**
 * Update test database to match user's schema changes
 */

require_once 'src/bootstrap.php';

echo "Updating test database to match user's schema changes...\n";

try {
    // Check current schema
    $columns = $pdo->query('PRAGMA table_info(send_money)')->fetchAll(PDO::FETCH_ASSOC);
    $columnNames = array_column($columns, 'name');
    
    echo "Current columns: " . implode(', ', $columnNames) . "\n";
    
    // Update schema to match user's changes
    if (in_array('invoice_no', $columnNames)) {
        echo "Renaming invoice_no to invoice...\n";
        $pdo->exec('ALTER TABLE send_money RENAME COLUMN invoice_no TO invoice');
    }
    
    if (in_array('amount', $columnNames)) {
        echo "Renaming amount to send_amount...\n";
        $pdo->exec('ALTER TABLE send_money RENAME COLUMN amount TO send_amount');
    }
    
    if (in_array('currency', $columnNames)) {
        echo "Renaming currency to send_curr...\n";
        $pdo->exec('ALTER TABLE send_money RENAME COLUMN currency TO send_curr');
    }
    
    // Update audit table
    $auditColumns = $pdo->query('PRAGMA table_info(sidecar_beneficiary_amend_audits)')->fetchAll(PDO::FETCH_ASSOC);
    $auditColumnNames = array_column($auditColumns, 'name');
    
    if (in_array('invoice_no', $auditColumnNames)) {
        echo "Renaming audit table invoice_no to invoice...\n";
        $pdo->exec('ALTER TABLE sidecar_beneficiary_amend_audits RENAME COLUMN invoice_no TO invoice');
    }
    
    // Verify changes
    echo "\nUpdated schema:\n";
    $newColumns = $pdo->query('PRAGMA table_info(send_money)')->fetchAll(PDO::FETCH_ASSOC);
    foreach ($newColumns as $col) {
        echo "- {$col['name']} ({$col['type']})\n";
    }
    
    echo "\nSample data:\n";
    $sampleData = $pdo->query('SELECT * FROM send_money LIMIT 1')->fetch(PDO::FETCH_ASSOC);
    if ($sampleData) {
        foreach ($sampleData as $key => $value) {
            echo "- {$key}: {$value}\n";
        }
    }
    
    echo "\n✅ Database schema updated successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
