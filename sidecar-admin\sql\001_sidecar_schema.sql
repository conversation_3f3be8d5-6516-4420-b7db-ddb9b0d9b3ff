-- Sidecar Admin Database Schema
-- This creates all sidecar tables with sidecar_ prefix
-- Does not modify vendor schema

-- Auth tables
CREATE TABLE IF NOT EXISTS sidecar_users (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  email VARCHAR(255) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  role ENUM('Admin','Compliance','Finance','Viewer') NOT NULL DEFAULT 'Viewer',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS sidecar_sessions (
  id CHAR(36) PRIMARY KEY,
  user_id BIGINT UNSIGNED NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NULL,
  FOREIGN KEY (user_id) REFERENCES sidecar_users(id) ON DELETE CASCADE
);

-- Beneficiary amend audit
CREATE TABLE IF NOT EXISTS sidecar_beneficiary_amend_audits (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  tx_id CHAR(36) NOT NULL,
  invoice VARCHAR(100) NOT NULL,
  changed_by_user_id BIGINT UNSIGNED NOT NULL,
  old_name VARCHAR(255),
  new_name VARCHAR(255),
  old_email VARCHAR(255),
  new_email VARCHAR(255),
  old_phone VARCHAR(50),
  new_phone VARCHAR(50),
  reason TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uniq_ba_tx (tx_id),
  INDEX idx_invoice (invoice),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (changed_by_user_id) REFERENCES sidecar_users(id)
);

-- Accounts & ledger for double-entry bookkeeping
CREATE TABLE IF NOT EXISTS sidecar_accounts (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  owner_user_id BIGINT UNSIGNED NOT NULL,  -- references vendor users.id
  account_type ENUM('Agent','NestblockCash','NestblockWallet') NOT NULL,
  send_curr CHAR(3) NOT NULL DEFAULT 'USD',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uniq_acct (owner_user_id, account_type, send_curr),
  INDEX idx_owner_type (owner_user_id, account_type)
);

CREATE TABLE IF NOT EXISTS sidecar_ledger_entries (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  tx_id CHAR(36) NOT NULL,
  account_id BIGINT UNSIGNED NOT NULL,
  direction ENUM('Debit','Credit') NOT NULL,
  send_amount DECIMAL(18,2) NOT NULL CHECK (send_amount > 0),
  meta JSON NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_tx_id (tx_id),
  INDEX idx_account_id (account_id),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (account_id) REFERENCES sidecar_accounts(id)
);

-- Sync/audit of vendor updates we performed (both parties)
CREATE TABLE IF NOT EXISTS sidecar_vendor_sync_audits (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  tx_id CHAR(36) NOT NULL,
  agent_user_id BIGINT UNSIGNED NOT NULL,
  counterparty_user_id BIGINT UNSIGNED NOT NULL,
  counterparty_type ENUM('NestblockCash','NestblockWallet') NOT NULL,
  send_curr CHAR(3) NOT NULL DEFAULT 'USD',
  agent_delta DECIMAL(18,2) NOT NULL,         -- + increases agent, - decreases
  counterparty_delta DECIMAL(18,2) NOT NULL,  -- the opposite
  agent_before DECIMAL(18,2) NOT NULL,
  agent_after DECIMAL(18,2) NOT NULL,
  counterparty_before DECIMAL(18,2) NOT NULL,
  counterparty_after DECIMAL(18,2) NOT NULL,
  created_by_user_id BIGINT UNSIGNED NOT NULL,
  reason TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uniq_sync_tx (tx_id),
  INDEX idx_agent (agent_user_id),
  INDEX idx_counterparty (counterparty_user_id),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (created_by_user_id) REFERENCES sidecar_users(id)
);

-- Rate limiting table
CREATE TABLE IF NOT EXISTS sidecar_rate_limits (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  identifier VARCHAR(255) NOT NULL, -- IP or user_id
  requests INT NOT NULL DEFAULT 1,
  window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uniq_identifier (identifier),
  INDEX idx_window_start (window_start)
);

-- CSRF tokens
CREATE TABLE IF NOT EXISTS sidecar_csrf_tokens (
  token CHAR(32) PRIMARY KEY,
  user_id BIGINT UNSIGNED NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NOT NULL,
  INDEX idx_user_id (user_id),
  INDEX idx_expires_at (expires_at),
  FOREIGN KEY (user_id) REFERENCES sidecar_users(id) ON DELETE CASCADE
);
