<?php
/**
 * Authentication Controller
 */

class AuthController {
    
    /**
     * Handle login request
     */
    public function login($params = []) {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(baseUrl('login'));
        }
        
        $email = sanitizeInput($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        
        // Validate input
        if (empty($email) || empty($password)) {
            setFlashMessage('error', 'Email and password are required');
            redirect(baseUrl('login'));
        }
        
        if (!isValidEmail($email)) {
            setFlashMessage('error', 'Invalid email format');
            redirect(baseUrl('login'));
        }
        
        try {
            // Attempt authentication
            $user = authenticateUser($email, $password);
            
            if ($user) {
                loginUser($user);
                setFlashMessage('success', 'Welcome back, ' . htmlspecialchars($user['email']));
                
                // Redirect to intended page or dashboard
                $redirectTo = $_SESSION['intended_url'] ?? baseUrl();
                unset($_SESSION['intended_url']);
                redirect($redirectTo);
            } else {
                setFlashMessage('error', 'Invalid email or password');
                redirect(baseUrl('login'));
            }
        } catch (Exception $e) {
            logMessage('error', 'Login error: ' . $e->getMessage(), [
                'email' => $email,
                'ip' => $_SERVER['REMOTE_ADDR']
            ]);
            
            setFlashMessage('error', 'An error occurred during login. Please try again.');
            redirect(baseUrl('login'));
        }
    }
    
    /**
     * Handle logout request
     */
    public function logout($params = []) {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(baseUrl());
        }
        
        try {
            logoutUser();
            setFlashMessage('success', 'You have been logged out successfully');
        } catch (Exception $e) {
            logMessage('error', 'Logout error: ' . $e->getMessage());
            setFlashMessage('error', 'An error occurred during logout');
        }
        
        redirect(baseUrl('login'));
    }
    
    /**
     * Show user profile
     */
    public function profile($params = []) {
        requireAuth();
        
        $user = getCurrentUser();
        include __DIR__ . '/../views/profile.php';
    }
    
    /**
     * Update user profile
     */
    public function updateProfile($params = []) {
        requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(baseUrl('profile'));
        }
        
        $user = getCurrentUser();
        $currentPassword = $_POST['current_password'] ?? '';
        $newPassword = $_POST['new_password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        try {
            // Verify current password
            if (!authenticateUser($user['email'], $currentPassword)) {
                setFlashMessage('error', 'Current password is incorrect');
                redirect(baseUrl('profile'));
            }
            
            // Validate new password
            if (strlen($newPassword) < 8) {
                setFlashMessage('error', 'New password must be at least 8 characters long');
                redirect(baseUrl('profile'));
            }
            
            if ($newPassword !== $confirmPassword) {
                setFlashMessage('error', 'New password and confirmation do not match');
                redirect(baseUrl('profile'));
            }
            
            // Update password
            updateUserPassword($user['id'], $newPassword);
            setFlashMessage('success', 'Password updated successfully');
            
        } catch (Exception $e) {
            logMessage('error', 'Profile update error: ' . $e->getMessage(), [
                'user_id' => $user['id']
            ]);
            
            setFlashMessage('error', 'An error occurred while updating your profile');
        }
        
        redirect(baseUrl('profile'));
    }
    
    /**
     * Show user management (Admin only)
     */
    public function users($params = []) {
        requireRole(['Admin']);
        
        try {
            $users = getAllUsers();
            include __DIR__ . '/../views/users.php';
        } catch (Exception $e) {
            logMessage('error', 'Users list error: ' . $e->getMessage());
            setFlashMessage('error', 'An error occurred while loading users');
            redirect(baseUrl());
        }
    }
    
    /**
     * Create new user (Admin only)
     */
    public function createUser($params = []) {
        requireRole(['Admin']);
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(baseUrl('users'));
        }
        
        $email = sanitizeInput($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $role = sanitizeInput($_POST['role'] ?? 'Viewer');
        
        try {
            // Validate input
            if (empty($email) || empty($password)) {
                setFlashMessage('error', 'Email and password are required');
                redirect(baseUrl('users'));
            }
            
            if (!isValidEmail($email)) {
                setFlashMessage('error', 'Invalid email format');
                redirect(baseUrl('users'));
            }
            
            if (strlen($password) < 8) {
                setFlashMessage('error', 'Password must be at least 8 characters long');
                redirect(baseUrl('users'));
            }
            
            // Create user
            $userId = createUser($email, $password, $role);
            setFlashMessage('success', "User created successfully with ID: $userId");
            
        } catch (Exception $e) {
            logMessage('error', 'User creation error: ' . $e->getMessage(), [
                'email' => $email,
                'role' => $role
            ]);
            
            setFlashMessage('error', $e->getMessage());
        }
        
        redirect(baseUrl('users'));
    }
    
    /**
     * Update user role (Admin only)
     */
    public function updateUserRole($params = []) {
        requireRole(['Admin']);
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(baseUrl('users'));
        }
        
        $userId = (int)($_POST['user_id'] ?? 0);
        $newRole = sanitizeInput($_POST['role'] ?? '');
        
        try {
            if (!$userId) {
                setFlashMessage('error', 'Invalid user ID');
                redirect(baseUrl('users'));
            }
            
            updateUserRole($userId, $newRole);
            setFlashMessage('success', 'User role updated successfully');
            
        } catch (Exception $e) {
            logMessage('error', 'User role update error: ' . $e->getMessage(), [
                'user_id' => $userId,
                'new_role' => $newRole
            ]);
            
            setFlashMessage('error', $e->getMessage());
        }
        
        redirect(baseUrl('users'));
    }
}
