<?php
/**
 * Test setup script for beneficiary amendment system
 */

require_once 'src/bootstrap.php';

echo "Testing database connection...\n";

try {
    $result = $pdo->query('SELECT 1 as test');
    echo "Database connection: OK\n";
    
    // Check if send_money table exists
    $tables = $pdo->query('SHOW TABLES LIKE "send_money"')->fetchAll();
    if (empty($tables)) {
        echo "WARNING: send_money table does not exist. Creating sample table...\n";
        $pdo->exec('CREATE TABLE send_money (
            id INT AUTO_INCREMENT PRIMARY KEY,
            invoice VARCHAR(100) NOT NULL UNIQUE,
            recipient_name VARCHAR(255) NOT NULL,
            recipient_email VARCHAR(255) NOT NULL,
            recipient_contact_no VARCHAR(50) NOT NULL,
            status VARCHAR(50) NOT NULL DEFAULT "Pending",
            send_amount DECIMAL(10,2) NOT NULL,
            send_curr VARCHAR(3) NOT NULL DEFAULT "USD",
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )');
        
        // Insert sample data
        $pdo->exec('INSERT INTO send_money (invoice, recipient_name, recipient_email, recipient_contact_no, status, send_amount) VALUES
            ("INV-001", "John Doe", "<EMAIL>", "+1234567890", "Pending", 500.00),
            ("INV-002", "Jane Smith", "<EMAIL>", "+0987654321", "Processing", 750.00),
            ("INV-003", "Bob Johnson", "<EMAIL>", "+1122334455", "PaidOut", 300.00)
        ');
        echo "Sample send_money table created with test data.\n";
    } else {
        echo "send_money table exists: OK\n";
    }
    
    // Run the audit table migration
    echo "Running audit table migration...\n";
    $migration = file_get_contents('sql/003_update_beneficiary_audit_schema.sql');
    $statements = array_filter(array_map('trim', explode(';', $migration)));
    
    foreach ($statements as $statement) {
        if (!empty($statement) && !str_starts_with($statement, '--')) {
            try {
                $pdo->exec($statement);
                echo "Executed: " . substr($statement, 0, 50) . "...\n";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate column') !== false) {
                    echo "Column already exists, skipping...\n";
                } else {
                    echo "Error: " . $e->getMessage() . "\n";
                }
            }
        }
    }
    
    // Test the search API
    echo "\nTesting search functionality...\n";
    $searchResults = $pdo->query('SELECT invoice, recipient_name, recipient_email, recipient_contact_no, status, send_amount, send_curr FROM send_money LIMIT 3')->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Found " . count($searchResults) . " test remittances:\n";
    foreach ($searchResults as $result) {
        echo "- {$result['invoice']}: {$result['recipient_name']} ({$result['status']})\n";
    }
    
    echo "\nSetup completed successfully!\n";
    echo "\nYou can now:\n";
    echo "1. Navigate to /beneficiaries/amend\n";
    echo "2. Search for invoice numbers: INV-001, INV-002, or INV-003\n";
    echo "3. Test the amendment functionality\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
