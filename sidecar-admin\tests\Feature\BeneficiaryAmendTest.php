<?php

use PHPUnit\Framework\TestCase;

class BeneficiaryAmendTest extends TestCase {
    
    private $userId;
    private $beneficiaryId;
    private $remittanceId;
    
    protected function setUp(): void {
        global $pdo;
        
        // Clean up
        cleanupTestData();
        
        // Create test user
        $this->userId = createTestUser('<EMAIL>', 'Compliance');
        
        // Create test beneficiary and remittance
        $this->beneficiaryId = createTestBeneficiary('Original Name', '+1234567890');
        $this->remittanceId = createTestRemittance($this->beneficiaryId, 'Pending');
        
        // Login test user
        loginTestUser($this->userId);
    }
    
    protected function tearDown(): void {
        cleanupTestData();
    }
    
    public function testSuccessfulBeneficiaryAmendment() {
        global $pdo;
        
        require_once __DIR__ . '/../../src/controllers/BeneficiaryController.php';
        $controller = new BeneficiaryController();
        
        // Simulate POST request
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'remittance_id' => $this->remittanceId,
            'name' => 'Updated Name',
            'phone' => '+9876543210',
            'reason' => 'Customer requested name correction due to passport update'
        ];
        
        // Capture output to prevent redirect
        ob_start();
        
        try {
            $controller->amend();
        } catch (Exception $e) {
            // Expected due to redirect
            if (strpos($e->getMessage(), 'Cannot modify header') === false) {
                throw $e;
            }
        }
        
        ob_end_clean();
        
        // Verify beneficiary was updated in vendor table
        $updatedBeneficiary = fetchOne($pdo, 
            'SELECT name, phone FROM beneficiaries WHERE id = ?', 
            [$this->beneficiaryId]
        );
        
        $this->assertEquals('Updated Name', $updatedBeneficiary['name']);
        $this->assertEquals('+9876543210', $updatedBeneficiary['phone']);
        
        // Verify audit record was created
        $auditRecord = fetchOne($pdo, 
            'SELECT * FROM sidecar_beneficiary_amend_audits WHERE remittance_id = ?', 
            [$this->remittanceId]
        );
        
        $this->assertNotNull($auditRecord);
        $this->assertEquals($this->remittanceId, $auditRecord['remittance_id']);
        $this->assertEquals($this->beneficiaryId, $auditRecord['beneficiary_id']);
        $this->assertEquals($this->userId, $auditRecord['changed_by_user_id']);
        $this->assertEquals('Original Name', $auditRecord['old_name']);
        $this->assertEquals('Updated Name', $auditRecord['new_name']);
        $this->assertEquals('+1234567890', $auditRecord['old_phone']);
        $this->assertEquals('+9876543210', $auditRecord['new_phone']);
        $this->assertNotEmpty($auditRecord['tx_id']);
    }
    
    public function testAmendmentBlockedForPaidOutRemittance() {
        global $pdo;
        
        // Update remittance status to PaidOut
        updateRecord($pdo, 'remittances', 
            ['status' => 'PaidOut'], 
            'id = ?', 
            [$this->remittanceId]
        );
        
        require_once __DIR__ . '/../../src/controllers/BeneficiaryController.php';
        $controller = new BeneficiaryController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'remittance_id' => $this->remittanceId,
            'name' => 'Updated Name',
            'phone' => '+9876543210',
            'reason' => 'Should be blocked'
        ];
        
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Cannot amend beneficiary for remittance with status: PaidOut');
        
        $controller->amend();
    }
    
    public function testAmendmentBlockedForCancelledRemittance() {
        global $pdo;
        
        // Update remittance status to Cancelled
        updateRecord($pdo, 'remittances', 
            ['status' => 'Cancelled'], 
            'id = ?', 
            [$this->remittanceId]
        );
        
        require_once __DIR__ . '/../../src/controllers/BeneficiaryController.php';
        $controller = new BeneficiaryController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'remittance_id' => $this->remittanceId,
            'name' => 'Updated Name',
            'phone' => '+9876543210',
            'reason' => 'Should be blocked'
        ];
        
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Cannot amend beneficiary for remittance with status: Cancelled');
        
        $controller->amend();
    }
    
    public function testAmendmentValidation() {
        require_once __DIR__ . '/../../src/controllers/BeneficiaryController.php';
        $controller = new BeneficiaryController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        
        // Test missing remittance ID
        $_POST = [
            'name' => 'Updated Name',
            'phone' => '+9876543210',
            'reason' => 'Valid reason'
        ];
        
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid remittance ID');
        
        $controller->amend();
    }
    
    public function testAmendmentRequiresValidReason() {
        require_once __DIR__ . '/../../src/controllers/BeneficiaryController.php';
        $controller = new BeneficiaryController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'remittance_id' => $this->remittanceId,
            'name' => 'Updated Name',
            'phone' => '+9876543210',
            'reason' => 'Too short' // Less than 5 characters
        ];
        
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Reason is required and must be at least 5 characters');
        
        $controller->amend();
    }
    
    public function testNoChangeDetection() {
        require_once __DIR__ . '/../../src/controllers/BeneficiaryController.php';
        $controller = new BeneficiaryController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'remittance_id' => $this->remittanceId,
            'name' => 'Original Name', // Same as current
            'phone' => '+1234567890',  // Same as current
            'reason' => 'No actual changes made'
        ];
        
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('No changes detected in beneficiary details');
        
        $controller->amend();
    }
    
    public function testAuditTrailIntegrity() {
        global $pdo;
        
        require_once __DIR__ . '/../../src/controllers/BeneficiaryController.php';
        $controller = new BeneficiaryController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'remittance_id' => $this->remittanceId,
            'name' => 'Audit Test Name',
            'phone' => '+5555555555',
            'reason' => 'Testing audit trail integrity and completeness'
        ];
        
        ob_start();
        try {
            $controller->amend();
        } catch (Exception $e) {
            // Expected due to redirect
        }
        ob_end_clean();
        
        // Verify audit record completeness
        $auditRecord = fetchOne($pdo, 
            'SELECT * FROM sidecar_beneficiary_amend_audits WHERE remittance_id = ?', 
            [$this->remittanceId]
        );
        
        $this->assertNotNull($auditRecord);
        $this->assertNotEmpty($auditRecord['tx_id']);
        $this->assertEquals(36, strlen($auditRecord['tx_id'])); // UUID length
        $this->assertNotNull($auditRecord['created_at']);
        $this->assertEquals('Testing audit trail integrity and completeness', $auditRecord['reason']);
        
        // Verify uniqueness constraint on tx_id
        $duplicateCount = fetchOne($pdo, 
            'SELECT COUNT(*) as count FROM sidecar_beneficiary_amend_audits WHERE tx_id = ?', 
            [$auditRecord['tx_id']]
        )['count'];
        
        $this->assertEquals(1, $duplicateCount);
    }
}
