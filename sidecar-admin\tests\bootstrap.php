<?php
/**
 * PHPUnit Bootstrap
 */

// Include the main bootstrap
require_once __DIR__ . '/../src/bootstrap.php';

// Test helper functions
function createTestUser($email = '<EMAIL>', $role = 'Admin') {
    global $pdo;
    
    // Clean up existing test user
    executeQuery($pdo, 'DELETE FROM sidecar_users WHERE email = ?', [$email]);
    
    return insertRecord($pdo, 'sidecar_users', [
        'email' => $email,
        'password_hash' => password_hash('password123', PASSWORD_DEFAULT),
        'role' => $role
    ]);
}

function createTestAgent($id = 9999, $balance = 1000.00) {
    global $pdo;
    
    // Clean up existing test agent
    executeQuery($pdo, 'DELETE FROM users WHERE id = ?', [$id]);
    
    insertRecord($pdo, 'users', [
        'id' => $id,
        'email' => "agent{$id}@test.com",
        'name' => "Test Agent {$id}",
        'balance' => $balance
    ]);
    
    return $id;
}

function createTestRemittance($beneficiaryId = null, $status = 'Pending') {
    global $pdo;
    
    if (!$beneficiaryId) {
        $beneficiaryId = createTestBeneficiary();
    }
    
    return insertRecord($pdo, 'remittances', [
        'sender_id' => 2001,
        'beneficiary_id' => $beneficiaryId,
        'send_amount' => 500.00,
        'send_curr' => 'USD',
        'status' => $status
    ]);
}

function createTestBeneficiary($name = 'Test Beneficiary', $phone = '+**********') {
    global $pdo;
    
    return insertRecord($pdo, 'beneficiaries', [
        'name' => $name,
        'phone' => $phone,
        'email' => '<EMAIL>'
    ]);
}

function cleanupTestData() {
    global $pdo;
    
    // Clean up sidecar tables
    $sidecarTables = [
        'sidecar_vendor_sync_audits',
        'sidecar_ledger_entries',
        'sidecar_accounts',
        'sidecar_beneficiary_amend_audits',
        'sidecar_sessions',
        'sidecar_users',
        'sidecar_rate_limits',
        'sidecar_csrf_tokens'
    ];
    
    foreach ($sidecarTables as $table) {
        executeQuery($pdo, "DELETE FROM $table WHERE 1=1");
    }
    
    // Clean up test vendor data
    executeQuery($pdo, 'DELETE FROM transactions WHERE trx_id LIKE "SC-%"');
    executeQuery($pdo, 'DELETE FROM remittances WHERE sender_id >= 9000');
    executeQuery($pdo, 'DELETE FROM beneficiaries WHERE email LIKE "%@test.com"');
    executeQuery($pdo, 'DELETE FROM users WHERE id >= 9000 OR email LIKE "%@test.com"');
}

function loginTestUser($userId) {
    global $pdo;
    
    $user = fetchOne($pdo, 'SELECT id, email, role FROM sidecar_users WHERE id = ?', [$userId]);
    if ($user) {
        $_SESSION['user'] = $user;
        $_SESSION['login_time'] = time();
        return true;
    }
    return false;
}

function assertBalanceEquals($expected, $actual, $message = '', $delta = 0.01) {
    if (abs($expected - $actual) > $delta) {
        throw new Exception($message ?: "Balance mismatch: expected {$expected}, got {$actual}");
    }
}

function getVendorBalance($userId) {
    global $pdo;
    $result = fetchOne($pdo, 'SELECT balance FROM users WHERE id = ?', [$userId]);
    return $result ? (float)$result['balance'] : 0.0;
}

function getSidecarLedgerBalance($ownerUserId, $accountType, $send_curr = 'USD') {
    global $pdo;
    
    $account = fetchOne($pdo, 
        'SELECT id FROM sidecar_accounts WHERE owner_user_id = ? AND account_type = ? AND send_curr = ?',
        [$ownerUserId, $accountType, $send_curr]
    );
    
    if (!$account) {
        return 0.0;
    }
    
    $balance = fetchOne($pdo, 
        'SELECT SUM(CASE WHEN direction = "Debit" THEN send_amount ELSE -send_amount END) as balance 
         FROM sidecar_ledger_entries WHERE account_id = ?',
        [$account['id']]
    );
    
    return (float)($balance['balance'] ?? 0);
}

function countLedgerEntries($txId) {
    global $pdo;
    $result = fetchOne($pdo, 'SELECT COUNT(*) as count FROM sidecar_ledger_entries WHERE tx_id = ?', [$txId]);
    return (int)($result['count'] ?? 0);
}

function countVendorTransactions($txIdPattern) {
    global $pdo;
    $result = fetchOne($pdo, 'SELECT COUNT(*) as count FROM transactions WHERE trx_id LIKE ?', [$txIdPattern]);
    return (int)($result['count'] ?? 0);
}

// Set up test environment
if (!defined('PHPUNIT_RUNNING')) {
    define('PHPUNIT_RUNNING', true);
}

// Clean up before tests
cleanupTestData();
