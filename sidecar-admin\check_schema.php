<?php
require_once 'src/bootstrap.php';

echo "Checking current database schema...\n";

try {
    $columns = $pdo->query('PRAGMA table_info(send_money)')->fetchAll(PDO::FETCH_ASSOC);
    echo "send_money table columns:\n";
    foreach ($columns as $col) {
        echo "- {$col['name']} ({$col['type']})\n";
    }
    
    echo "\nChecking audit table columns:\n";
    $auditColumns = $pdo->query('PRAGMA table_info(sidecar_beneficiary_amend_audits)')->fetchAll(PDO::FETCH_ASSOC);
    foreach ($auditColumns as $col) {
        echo "- {$col['name']} ({$col['type']})\n";
    }
    
    echo "\nSample data from send_money:\n";
    $sampleData = $pdo->query('SELECT * FROM send_money LIMIT 1')->fetch(PDO::FETCH_ASSOC);
    if ($sampleData) {
        foreach ($sampleData as $key => $value) {
            echo "- {$key}: {$value}\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
