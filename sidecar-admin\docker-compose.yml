version: '3.8'

services:
  sidecar-admin:
    build: .
    ports:
      - "8080:80"
    volumes:
      - .:/var/www/html
      - ./logs:/var/www/html/logs
    environment:
      - APP_ENV=local
    env_file:
      - .env
    depends_on:
      - mysql
    networks:
      - sidecar-network

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: vendor_db
      MYSQL_USER: sidecar_ops
      MYSQL_PASSWORD: change_me
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    networks:
      - sidecar-network

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    environment:
      PMA_HOST: mysql
      PMA_USER: root
      PMA_PASSWORD: rootpassword
    ports:
      - "8081:80"
    depends_on:
      - mysql
    networks:
      - sidecar-network

volumes:
  mysql_data:

networks:
  sidecar-network:
    driver: bridge
