<?php
/**
 * Helper Functions
 */

/**
 * Generate UUID v4
 */
function generateUuid() {
    $data = random_bytes(16);
    $data[6] = chr(ord($data[6]) & 0x0f | 0x40); // version 4
    $data[8] = chr(ord($data[8]) & 0x3f | 0x80); // variant bits
    
    return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
}

/**
 * Generate random string
 */
function generateRandomString($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Sanitize input
 */
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate decimal send_amount
 */
function isValidsend_amount($send_amount) {
    return is_numeric($send_amount) && $send_amount > 0 && preg_match('/^\d+(\.\d{1,2})?$/', $send_amount);
}

/**
 * Format send_curr send_amount
 */
function formatCurrency($send_amount, $send_curr = 'USD') {
    return number_format((float)$send_amount, 2, '.', ',') . ' ' . $send_curr;
}

/**
 * Get current user from session
 */
function getCurrentUser() {
    return $_SESSION['user'] ?? null;
}

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    return isset($_SESSION['user']) && isset($_SESSION['user']['id']);
}

/**
 * Check if user has required role
 */
function hasRole($requiredRoles) {
    $user = getCurrentUser();
    if (!$user) {
        return false;
    }
    
    if (is_string($requiredRoles)) {
        $requiredRoles = [$requiredRoles];
    }
    
    return in_array($user['role'], $requiredRoles);
}

/**
 * Redirect to URL
 */
function redirect($url, $statusCode = 302) {
    header("Location: $url", true, $statusCode);
    exit;
}

/**
 * Get base URL
 */
function baseUrl($path = '') {
    global $config;
    return rtrim($config['app']['url'], '/') . '/' . ltrim($path, '/');
}

/**
 * Generate CSRF token
 */
function generateCsrfToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = generateRandomString(32);
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCsrfToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Set flash message
 */
function setFlashMessage($type, $message) {
    $_SESSION['flash'][$type] = $message;
}

/**
 * Get and clear flash messages
 */
function getFlashMessages() {
    $messages = $_SESSION['flash'] ?? [];
    unset($_SESSION['flash']);
    return $messages;
}

/**
 * Log message
 */
function logMessage($level, $message, $context = []) {
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = $context ? ' ' . json_encode($context) : '';
    $logEntry = "[$timestamp] $level: $message$contextStr" . PHP_EOL;
    
    $logFile = __DIR__ . '/../logs/sidecar.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * Validate deposit/withdraw operation type
 */
function isValidDepositWithdrawOperation($operation) {
    $validOperations = [
        'AgentDepositCash',
        'AgentWithdrawCash',
        'AgentDepositWallet',
        'AgentWithdrawWallet'
    ];
    return in_array($operation, $validOperations);
}

/**
 * Validate transfer direction (legacy support)
 * @deprecated Use isValidDepositWithdrawOperation instead
 */
function isValidTransferDirection($direction) {
    return isValidDepositWithdrawOperation($direction);
}

/**
 * Get counterparty info from operation type
 */
function getCounterpartyInfo($operation) {
    global $config;

    switch ($operation) {
        case 'AgentDepositCash':
        case 'AgentWithdrawCash':
        case 'AgentToNestblockCash':  // Legacy support
        case 'NestblockCashToAgent':  // Legacy support
            return [
                'user_id' => $config['business']['nestblock_cash_user_id'],
                'type' => 'NestblockCash'
            ];
        case 'AgentDepositWallet':
        case 'AgentWithdrawWallet':
        case 'AgentToNestblockWallet':  // Legacy support
        case 'NestblockWalletToAgent':  // Legacy support
            return [
                'user_id' => $config['business']['nestblock_wallet_user_id'],
                'type' => 'NestblockWallet'
            ];
        default:
            throw new InvalidArgumentException('Invalid operation type');
    }
}

/**
 * Get posting rules for deposit/withdraw operation
 */
function getPostingRules($operation) {
    switch ($operation) {
        case 'AgentDepositCash':
            // For deposits: both accounts increase, so we debit both (asset increases)
            return ['debit' => 'Agent', 'credit' => 'NestblockCash'];
        case 'AgentWithdrawCash':
            // For withdrawals: both accounts decrease, so we credit both (asset decreases)
            return ['debit' => 'NestblockCash', 'credit' => 'Agent'];
        case 'AgentDepositWallet':
            // For deposits: both accounts increase, so we debit both (asset increases)
            return ['debit' => 'Agent', 'credit' => 'NestblockWallet'];
        case 'AgentWithdrawWallet':
            // For withdrawals: both accounts decrease, so we credit both (asset decreases)
            return ['debit' => 'NestblockWallet', 'credit' => 'Agent'];
        // Legacy transfer support (maintains old zero-sum behavior)
        case 'AgentToNestblockCash':
            return ['debit' => 'NestblockCash', 'credit' => 'Agent'];
        case 'NestblockCashToAgent':
            return ['debit' => 'Agent', 'credit' => 'NestblockCash'];
        case 'AgentToNestblockWallet':
            return ['debit' => 'NestblockWallet', 'credit' => 'Agent'];
        case 'NestblockWalletToAgent':
            return ['debit' => 'Agent', 'credit' => 'NestblockWallet'];
        default:
            throw new InvalidArgumentException('Invalid operation type');
    }
}

/**
 * Get vendor balance changes for deposit/withdraw operation
 */
function getVendorBalanceChanges($operation, $amount) {
    switch ($operation) {
        case 'AgentDepositCash':
        case 'AgentDepositWallet':
            // Agent deposits: both agent balance and platform account increase
            return ['agent_delta' => $amount, 'counterparty_delta' => $amount];
        case 'AgentWithdrawCash':
        case 'AgentWithdrawWallet':
            // Agent withdraws: both agent balance and platform account decrease
            return ['agent_delta' => -$amount, 'counterparty_delta' => -$amount];
        // Legacy transfer support (maintains old zero-sum behavior)
        case 'AgentToNestblockCash':
        case 'AgentToNestblockWallet':
            return ['agent_delta' => -$amount, 'counterparty_delta' => $amount];
        case 'NestblockCashToAgent':
        case 'NestblockWalletToAgent':
            return ['agent_delta' => $amount, 'counterparty_delta' => -$amount];
        default:
            throw new InvalidArgumentException('Invalid operation type');
    }
}

/**
 * Generate vendor transaction ID
 */
function generateVendorTrxId($txId, $suffix) {
    global $config;
    return $config['business']['trx_prefix'] . '-' . $txId . '-' . $suffix;
}

/**
 * Check rate limit
 */
function checkRateLimit($identifier) {
    global $pdo, $config;
    
    $windowStart = date('Y-m-d H:i:s', time() - $config['security']['rate_limit_window']);
    
    // Clean old entries
    executeQuery($pdo, 
        'DELETE FROM sidecar_rate_limits WHERE window_start < ?', 
        [$windowStart]
    );
    
    // Get current count
    $current = fetchOne($pdo, 
        'SELECT requests FROM sidecar_rate_limits WHERE identifier = ?', 
        [$identifier]
    );
    
    if ($current) {
        if ($current['requests'] >= $config['security']['rate_limit_requests']) {
            return false; // Rate limit exceeded
        }
        
        // Increment counter
        executeQuery($pdo, 
            'UPDATE sidecar_rate_limits SET requests = requests + 1 WHERE identifier = ?', 
            [$identifier]
        );
    } else {
        // Create new entry
        insertRecord($pdo, 'sidecar_rate_limits', [
            'identifier' => $identifier,
            'requests' => 1,
            'window_start' => date('Y-m-d H:i:s')
        ]);
    }
    
    return true;
}
