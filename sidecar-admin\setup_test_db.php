<?php
/**
 * Simple test database setup
 */

// Create SQLite database for testing
$dbPath = __DIR__ . '/test.db';

try {
    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Creating test database...\n";
    
    // Create send_money table
    $pdo->exec('CREATE TABLE IF NOT EXISTS send_money (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice VARCHAR(100) NOT NULL UNIQUE,
        recipient_name VARCHAR(255) NOT NULL,
        recipient_email VARCHAR(255) NOT NULL,
        recipient_contact_no VARCHAR(50) NOT NULL,
        status VARCHAR(50) NOT NULL DEFAULT "Pending",
        send_amount DECIMAL(10,2) NOT NULL,
        send_curr VARCHAR(3) NOT NULL DEFAULT "USD",
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )');
    
    // Create sidecar tables
    $pdo->exec('CREATE TABLE IF NOT EXISTS sidecar_users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email VARCHAR(255) NOT NULL UNIQUE,
        password_hash VARCHAR(255) NOT NULL,
        role VARCHAR(50) NOT NULL,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )');
    
    $pdo->exec('CREATE TABLE IF NOT EXISTS sidecar_beneficiary_amend_audits (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tx_id VARCHAR(36) NOT NULL UNIQUE,
        invoice VARCHAR(100),
        remittance_id INTEGER,
        beneficiary_id INTEGER,
        old_name VARCHAR(255) NOT NULL,
        new_name VARCHAR(255) NOT NULL,
        old_email VARCHAR(255),
        new_email VARCHAR(255),
        old_phone VARCHAR(50) NOT NULL,
        new_phone VARCHAR(50) NOT NULL,
        reason TEXT NOT NULL,
        changed_by_user_id INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )');
    
    // Insert test data
    $pdo->exec('INSERT OR IGNORE INTO send_money (invoice, recipient_name, recipient_email, recipient_contact_no, status, send_amount) VALUES
        ("INV-001", "John Doe", "<EMAIL>", "+1234567890", "Pending", 500.00),
        ("INV-002", "Jane Smith", "<EMAIL>", "+0987654321", "Processing", 750.00),
        ("INV-003", "Bob Johnson", "<EMAIL>", "+1122334455", "PaidOut", 300.00)
    ');
    
    // Insert test user
    $pdo->exec('INSERT OR IGNORE INTO sidecar_users (email, password_hash, role) VALUES
        ("<EMAIL>", "$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", "Admin")
    ');
    
    echo "Test database created successfully!\n";
    echo "Database path: $dbPath\n";
    echo "\nTest data:\n";
    
    $results = $pdo->query('SELECT invoice, recipient_name, status FROM send_money')->fetchAll();
    foreach ($results as $row) {
        echo "- {$row['invoice']}: {$row['recipient_name']} ({$row['status']})\n";
    }
    
    echo "\nTest user: <EMAIL> / password\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
