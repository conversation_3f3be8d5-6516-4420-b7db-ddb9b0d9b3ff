<?php
require_once 'src/bootstrap.php';

echo "Creating missing audit tables...\n";

try {
    // Create sidecar_vendor_sync_audits table (SQLite version)
    $pdo->exec('CREATE TABLE IF NOT EXISTS sidecar_vendor_sync_audits (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tx_id VARCHAR(36) NOT NULL,
        agent_user_id INTEGER NOT NULL,
        counterparty_user_id INTEGER NOT NULL,
        counterparty_type VARCHAR(50) NOT NULL,
        send_curr VARCHAR(3) NOT NULL DEFAULT "USD",
        agent_delta DECIMAL(18,2) NOT NULL,
        counterparty_delta DECIMAL(18,2) NOT NULL,
        agent_before DECIMAL(18,2) NOT NULL,
        agent_after DECIMAL(18,2) NOT NULL,
        counterparty_before DECIMAL(18,2) NOT NULL,
        counterparty_after DECIMAL(18,2) NOT NULL,
        created_by_user_id INTEGER NOT NULL,
        reason TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )');
    
    echo "✅ Created sidecar_vendor_sync_audits table\n";
    
    // Create unique index
    $pdo->exec('CREATE UNIQUE INDEX IF NOT EXISTS uniq_sync_tx ON sidecar_vendor_sync_audits(tx_id)');
    echo "✅ Created unique index on tx_id\n";
    
    // Create other indexes
    $pdo->exec('CREATE INDEX IF NOT EXISTS idx_agent ON sidecar_vendor_sync_audits(agent_user_id)');
    $pdo->exec('CREATE INDEX IF NOT EXISTS idx_counterparty ON sidecar_vendor_sync_audits(counterparty_user_id)');
    $pdo->exec('CREATE INDEX IF NOT EXISTS idx_created_at ON sidecar_vendor_sync_audits(created_at)');
    echo "✅ Created additional indexes\n";
    
    // Verify table creation
    $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%audit%'")->fetchAll(PDO::FETCH_COLUMN);
    echo "\nAudit tables now available:\n";
    foreach ($tables as $table) {
        echo "- $table\n";
    }
    
    echo "\n✅ All missing tables created successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
