<?php
require_once 'src/bootstrap.php';

echo "Testing audit reports functionality...\n\n";

try {
    // Test 1: Check if we can run the audit query from ReportsController
    echo "1. Testing audit reports query...\n";
    
    $page = 1;
    $limit = 20;
    $offset = ($page - 1) * $limit;
    
    // This is the same query from ReportsController (SQLite compatible)
    $auditSql = "
        SELECT 'amendment' as type, tx_id, created_at,
               'Beneficiary amended for invoice ' || COALESCE(invoice, 'N/A') as description,
               (SELECT email FROM sidecar_users WHERE id = changed_by_user_id) as user_email,
               '{\"type\":\"amendment\",\"invoice\":\"' || COALESCE(invoice, '') ||
               '\",\"old_name\":\"' || COALESCE(old_name, '') ||
               '\",\"new_name\":\"' || COALESCE(new_name, '') ||
               '\",\"old_email\":\"' || COALESCE(old_email, '') ||
               '\",\"new_email\":\"' || COALESCE(new_email, '') ||
               '\",\"old_phone\":\"' || COALESCE(old_phone, '') ||
               '\",\"new_phone\":\"' || COALESCE(new_phone, '') ||
               '\",\"reason\":\"' || COALESCE(reason, '') || '\"}' as details
        FROM sidecar_beneficiary_amend_audits
        UNION ALL
        SELECT 'transfer' as type, tx_id, created_at,
               'Transfer: ' || counterparty_type || ' ↔ Agent (' || send_curr || ' ' || ABS(agent_delta) || ')' as description,
               (SELECT email FROM sidecar_users WHERE id = created_by_user_id) as user_email,
               '{\"type\":\"transfer\"}' as details
        FROM sidecar_vendor_sync_audits
        ORDER BY created_at DESC
        LIMIT $limit OFFSET $offset
    ";
    
    $audits = fetchAll($pdo, $auditSql, []);
    echo "   ✅ Audit query executed successfully\n";
    echo "   Found " . count($audits) . " audit records\n";
    
    // Test 2: Test dashboard query
    echo "\n2. Testing dashboard recent activity query...\n";
    
    $recentActivity = fetchAll($pdo, "
        SELECT 'amendment' as type, tx_id, created_at,
               'Beneficiary amended for invoice ' || COALESCE(invoice, 'N/A') as description,
               (SELECT email FROM sidecar_users WHERE id = changed_by_user_id) as user_email
        FROM sidecar_beneficiary_amend_audits
        UNION ALL
        SELECT 'transfer' as type, tx_id, created_at,
               'Transfer: ' || counterparty_type || ' ↔ Agent (' || send_curr || ' ' || ABS(agent_delta) || ')' as description,
               (SELECT email FROM sidecar_users WHERE id = created_by_user_id) as user_email
        FROM sidecar_vendor_sync_audits
        ORDER BY created_at DESC LIMIT 10
    ");
    
    echo "   ✅ Dashboard query executed successfully\n";
    echo "   Found " . count($recentActivity) . " recent activity records\n";
    
    // Test 3: Add some test audit data
    echo "\n3. Adding test audit data...\n";
    
    // Add a test amendment audit
    $testTxId = 'test-' . uniqid();
    $pdo->exec("INSERT INTO sidecar_beneficiary_amend_audits 
                (tx_id, invoice, changed_by_user_id, old_name, new_name, old_email, new_email, old_phone, new_phone, reason) 
                VALUES 
                ('$testTxId', 'INV-001', 1, 'Old Name', 'New Name', '<EMAIL>', '<EMAIL>', '+1111111111', '+2222222222', 'Test amendment')");
    
    echo "   ✅ Added test amendment audit record\n";
    
    // Test the query again
    $auditsAfter = fetchAll($pdo, $auditSql, []);
    echo "   ✅ Query still works after adding data\n";
    echo "   Now found " . count($auditsAfter) . " audit records\n";
    
    if (count($auditsAfter) > 0) {
        echo "   Sample audit record:\n";
        $sample = $auditsAfter[0];
        echo "     - Type: {$sample['type']}\n";
        echo "     - Description: {$sample['description']}\n";
        echo "     - User: {$sample['user_email']}\n";
        echo "     - Created: {$sample['created_at']}\n";
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "✅ ALL AUDIT REPORT TESTS PASSED!\n";
    echo str_repeat("=", 50) . "\n";
    
    echo "\nThe audit reports should now work correctly at:\n";
    echo "- /reports/audits\n";
    echo "- Dashboard recent activity section\n";
    
} catch (Exception $e) {
    echo "❌ Error during testing: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
