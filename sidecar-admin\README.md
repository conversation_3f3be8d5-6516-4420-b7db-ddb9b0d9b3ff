# Sidecar Admin

A standalone PHP "sidecar" admin system that adds two critical operations to an existing money transfer platform **without modifying vendor code or schema**:

1. **Remittance Beneficiary Amendment** - Update beneficiary details with full audit trail
2. **Agent Account Deposits** - Double-entry ledger system with vendor synchronization

## Features

- ✅ **Zero Vendor Impact** - No changes to existing vendor code or database schema
- ✅ **Double-Entry Bookkeeping** - Proper accounting with ledger entries
- ✅ **Vendor Synchronization** - Real-time balance updates and transaction records
- ✅ **Complete Audit Trail** - Every operation tracked with who/when/what/why
- ✅ **Role-Based Access Control** - Admin, Finance, Compliance, Viewer roles
- ✅ **Security First** - CSRF protection, rate limiting, session management
- ✅ **Reconciliation** - Automated balance verification between sidecar and vendor
- ✅ **Idempotent Operations** - UUID-based transaction IDs prevent duplicates

## Quick Start

### Prerequisites

- PHP 8.1+
- MySQL/MariaDB 8.0+
- Docker & Docker Compose (for development)

### Development Setup

1. **Clone and Setup**
   ```bash
   git clone <repository>
   cd sidecar-admin
   cp .env.example .env
   ```

2. **Configure Environment**
   Edit `.env` with your database credentials and business rules:
   ```env
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_NAME=your_vendor_db
   DB_USER=sidecar_ops
   DB_PASS=secure_password
   
   NESTBLOCK_CASH_USER_ID=1001
   NESTBLOCK_WALLET_USER_ID=1002
   ```

3. **Start with Docker**
   ```bash
   make up
   ```

4. **Initialize Database**
   ```bash
   make db-setup
   ```

5. **Access Application**
   - URL: http://localhost:8080
   - Default Admin: <EMAIL> / admin123

### Manual Setup (without Docker)

1. **Database Setup**
   ```bash
   mysql -u root -p < sql/001_sidecar_schema.sql
   mysql -u root -p < sql/002_seed_users.sql
   ```

2. **Web Server**
   Point your web server document root to `public/` directory.

3. **Permissions**
   ```bash
   chmod 755 logs/
   chmod +x bin/reconcile.php
   ```

## Usage

### Beneficiary Amendment

1. Navigate to **Beneficiary Amendment**
2. Enter remittance ID to search
3. Update beneficiary name and/or phone
4. Provide reason for change
5. Submit - creates audit record and updates vendor database

**Restrictions:**
- Cannot amend if remittance status is `PaidOut`, `Cancelled`, `Refunded`, or `Failed`
- Requires minimum 5-character reason
- Must have actual changes to proceed

### Agent Deposit/Withdraw

Manage agent account deposits and withdrawals with full audit trail.

1. Navigate to **Agent Operations** → **Deposit/Withdraw**
2. Search and select agent
3. Choose operation type:
   - **Deposit to Cash Account** - Agent deposits funds, both agent and NestblockCash balances increase
   - **Withdraw from Cash Account** - Agent withdraws funds, both agent and NestblockCash balances decrease
   - **Deposit to Wallet Account** - Agent deposits funds, both agent and NestblockWallet balances increase
   - **Withdraw from Wallet Account** - Agent withdraws funds, both agent and NestblockWallet balances decrease
4. Enter amount and reason
5. Submit - creates ledger entries, updates balances, and syncs with vendor

**Operation Types:**
- `AgentDepositCash` - Agent deposits to cash system (both balances increase)
- `AgentWithdrawCash` - Agent withdraws from cash system (both balances decrease)
- `AgentDepositWallet` - Agent deposits to wallet system (both balances increase)
- `AgentWithdrawWallet` - Agent withdraws from wallet system (both balances decrease)

**Legacy Transfer Support:**
The system still supports the original transfer operations for backward compatibility:
- `AgentToNestblockCash` - Agent transfers to cash system (zero-sum transfer)
- `NestblockCashToAgent` - Cash system transfers to agent (zero-sum transfer)
- `AgentToNestblockWallet` - Agent transfers to wallet system (zero-sum transfer)
- `NestblockWalletToAgent` - Wallet system transfers to agent (zero-sum transfer)

### Audit & Reporting

- **Audit Trail** - View all amendments and transfers with filters
- **Statements** - Balance reconciliation and transfer summaries
- **Reconciliation** - Automated nightly checks via `bin/reconcile.php`

## Architecture

### Database Design

**Sidecar Tables** (added to vendor database):
- `sidecar_users` - Admin user accounts
- `sidecar_accounts` - Chart of accounts for ledger
- `sidecar_ledger_entries` - Double-entry bookkeeping records
- `sidecar_beneficiary_amend_audits` - Amendment audit trail
- `sidecar_vendor_sync_audits` - Transfer audit trail
- `sidecar_sessions` - Session management
- `sidecar_csrf_tokens` - CSRF protection
- `sidecar_rate_limits` - Rate limiting

**Vendor Tables** (read/write without schema changes):
- `users` - Agent accounts (balance updates)
- `transactions` - Transaction history (inserts)
- `remittances` - Remittance records (read-only)
- `beneficiaries` - Beneficiary details (updates)

### Double-Entry Bookkeeping

Every transfer creates exactly 2 ledger entries:
```
Agent Transfer $500 to NestblockCash:
- Debit:  Agent Account      $500
- Credit: NestblockCash      $500
```

### Vendor Synchronization

For each transfer:
1. **Sidecar Ledger** - Creates double-entry records
2. **Vendor Balances** - Updates `users.balance` for both parties
3. **Vendor Transactions** - Inserts 2 records in `transactions` table
4. **Audit Trail** - Records complete before/after state

## Security

- **Authentication** - Session-based with secure cookies
- **Authorization** - Role-based access control (RBAC)
- **CSRF Protection** - Token validation on all forms
- **Rate Limiting** - Prevents abuse and brute force
- **Input Validation** - Comprehensive server-side validation
- **SQL Injection** - PDO prepared statements throughout
- **XSS Protection** - Output escaping in all views

## Testing

### Unit Tests
```bash
make test
```

### E2E Testing
```bash
# Use tests/e2e.http with REST Client
# Or run manual curl commands
curl -c cookies.txt -X POST -d "email=<EMAIL>&password=admin123" http://localhost:8080/login
curl -b cookies.txt http://localhost:8080/api/agents/search?q=agent
```

### Reconciliation
```bash
# Manual reconciliation
php bin/reconcile.php

# With reports
php bin/reconcile.php --csv --html

# Automated (add to crontab)
0 2 * * * /path/to/sidecar-admin/bin/reconcile.php
```

## API Endpoints

### Authentication
- `POST /login` - User login
- `POST /logout` - User logout

### Beneficiary Amendment
- `GET /beneficiary/amend?remittance_id=X` - Amendment form
- `POST /beneficiary/amend` - Submit amendment

### Agent Transfers
- `GET /agent/transfer` - Transfer form
- `POST /agent/transfer` - Submit transfer
- `GET /api/agents/search?q=term` - Search agents
- `GET /api/agents/{id}/balance` - Get agent balance

### Reports
- `GET /reports/audits` - Audit trail
- `GET /reports/statements` - Statements & reconciliation

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_HOST` | Database host | 127.0.0.1 |
| `DB_PORT` | Database port | 3306 |
| `DB_NAME` | Database name | - |
| `DB_USER` | Database user | - |
| `DB_PASS` | Database password | - |
| `SESSION_SECRET` | Session encryption key | - |
| `NESTBLOCK_CASH_USER_ID` | Cash system user ID | - |
| `NESTBLOCK_WALLET_USER_ID` | Wallet system user ID | - |
| `SET_FINAL_BALANCE_IN_VENDOR_TRANSACTIONS` | Update final_balance in transactions | true |

### Business Rules

Configure in `.env`:
- `DISALLOWED_REMITTANCE_STATUSES` - Statuses that block amendments
- `ALLOWED_CURRENCIES` - Supported currencies for transfers
- `MIN_TRANSFER_send_amount` / `MAX_TRANSFER_send_amount` - Transfer limits

## Deployment

### Production Checklist

1. **Environment**
   - [ ] Copy `.env.example` to `.env`
   - [ ] Set secure `SESSION_SECRET` (32+ characters)
   - [ ] Configure database credentials
   - [ ] Set correct `NESTBLOCK_*_USER_ID` values

2. **Database**
   - [ ] Run `sql/001_sidecar_schema.sql`
   - [ ] Run `sql/002_seed_users.sql`
   - [ ] Create dedicated database user with minimal privileges

3. **Web Server**
   - [ ] Point document root to `public/`
   - [ ] Enable HTTPS with valid SSL certificate
   - [ ] Configure appropriate PHP settings (memory_limit, max_execution_time)

4. **Security**
   - [ ] Change default admin password
   - [ ] Set restrictive file permissions
   - [ ] Configure firewall rules
   - [ ] Enable error logging (disable display_errors)

5. **Monitoring**
   - [ ] Set up log rotation for `logs/` directory
   - [ ] Configure cron job for reconciliation
   - [ ] Monitor disk space and database performance

### Cron Jobs

```bash
# Nightly reconciliation at 2 AM
0 2 * * * /path/to/sidecar-admin/bin/reconcile.php

# Weekly log cleanup
0 3 * * 0 find /path/to/sidecar-admin/logs -name "*.log" -mtime +30 -delete
```

## Troubleshooting

### Common Issues

**Database Connection Failed**
- Check `.env` database credentials
- Verify database server is running
- Ensure user has proper privileges

**Permission Denied**
- Check file permissions on `logs/` directory
- Verify web server user can write to logs
- Ensure `bin/reconcile.php` is executable

**Reconciliation Failures**
- Check `logs/app.log` for detailed errors
- Verify `NESTBLOCK_*_USER_ID` values are correct
- Run manual reconciliation: `php bin/reconcile.php`

**Session Issues**
- Verify `SESSION_SECRET` is set and consistent
- Check session storage permissions
- Clear browser cookies and retry

### Logs

- `logs/app.log` - Application logs
- `logs/reconciliation_*.csv` - Reconciliation reports
- `logs/reconciliation_*.html` - Reconciliation reports (HTML)

## Support

For issues and questions:
1. Check logs in `logs/` directory
2. Review this documentation
3. Run reconciliation manually to verify system state
4. Check database constraints and foreign keys

## Acceptance Criteria Validation

### ✅ Remittance Beneficiary Amendment
- [x] Read remittance + beneficiary from vendor DB
- [x] Allow admins to update beneficiary name and phone
- [x] Block amendments if remittance is paid out/cancelled
- [x] Record complete audit entry (who/when/what/why)
- [x] Use UUID transaction IDs for uniqueness

### ✅ Agent Account Deposits
- [x] 4 transfer directions (Agent↔NestblockCash, Agent↔NestblockWallet)
- [x] Double-entry ledger with proper debit/credit entries
- [x] Vendor balance synchronization (users.balance updates)
- [x] Vendor transaction records (transactions table inserts)
- [x] Complete audit trail with before/after balances
- [x] Insufficient funds validation

### ✅ Technical Requirements
- [x] Plain PHP 8.1+ (no framework)
- [x] No vendor code/schema changes
- [x] Bootstrap 5 UI with vanilla JavaScript
- [x] PDO with prepared statements
- [x] Session-based authentication
- [x] Role-based access control (Admin, Finance, Compliance, Viewer)
- [x] CSRF protection and rate limiting
- [x] Comprehensive input validation

### ✅ Database Design
- [x] Sidecar tables with `sidecar_` prefix
- [x] Foreign key relationships where applicable
- [x] Proper indexing for performance
- [x] UUID transaction IDs with uniqueness constraints
- [x] Audit tables with complete change tracking

### ✅ Security & Compliance
- [x] Authentication with secure session management
- [x] Authorization with role-based permissions
- [x] CSRF token validation on all forms
- [x] Rate limiting to prevent abuse
- [x] SQL injection prevention (PDO prepared statements)
- [x] XSS protection (output escaping)
- [x] Complete audit trail for compliance

### ✅ Reconciliation & Reporting
- [x] Nightly reconciliation job (`bin/reconcile.php`)
- [x] Ledger balance verification (debits = credits)
- [x] Agent balance consistency checks (sidecar vs vendor)
- [x] System account balance verification
- [x] Audit trail reporting with filters
- [x] CSV and HTML report generation

### ✅ Testing & Documentation
- [x] Comprehensive PHPUnit test suite
- [x] E2E HTTP test examples
- [x] Docker development environment
- [x] Complete README with setup instructions
- [x] Deployment checklist and troubleshooting guide

## License

Proprietary - Internal use only
