<?php
/**
 * Debug production database schema
 * This script will help identify the actual database structure
 */

require_once 'src/bootstrap.php';

echo "=== PRODUCTION DATABASE SCHEMA DEBUG ===\n\n";

try {
    // Check database type
    $driver = $pdo->getAttribute(PDO::ATTR_DRIVER_NAME);
    echo "Database Driver: $driver\n\n";
    
    // Get all tables
    if ($driver === 'mysql') {
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        echo "All Tables:\n";
        foreach ($tables as $table) {
            echo "- $table\n";
        }
        
        echo "\n" . str_repeat("=", 50) . "\n";
        
        // Check specific audit tables
        $auditTables = array_filter($tables, function($table) {
            return strpos($table, 'audit') !== false || strpos($table, 'sidecar') !== false;
        });
        
        echo "Audit/Sidecar Tables Found:\n";
        foreach ($auditTables as $table) {
            echo "\nTable: $table\n";
            echo str_repeat("-", 30) . "\n";
            
            $columns = $pdo->query("DESCRIBE $table")->fetchAll(PDO::FETCH_ASSOC);
            foreach ($columns as $col) {
                echo "- {$col['Field']} ({$col['Type']}) {$col['Null']} {$col['Key']}\n";
            }
            
            // Check record count
            $count = $pdo->query("SELECT COUNT(*) as count FROM $table")->fetch()['count'];
            echo "Records: $count\n";
        }
        
        // Check if sidecar_vendor_sync_audits exists
        echo "\n" . str_repeat("=", 50) . "\n";
        echo "Checking sidecar_vendor_sync_audits table...\n";
        
        $vendorSyncExists = $pdo->query("SHOW TABLES LIKE 'sidecar_vendor_sync_audits'")->fetch();
        
        if ($vendorSyncExists) {
            echo "✅ sidecar_vendor_sync_audits table EXISTS\n";
            $vendorColumns = $pdo->query("DESCRIBE sidecar_vendor_sync_audits")->fetchAll(PDO::FETCH_ASSOC);
            echo "Columns:\n";
            foreach ($vendorColumns as $col) {
                echo "- {$col['Field']} ({$col['Type']})\n";
            }
        } else {
            echo "❌ sidecar_vendor_sync_audits table does NOT exist\n";
            echo "This is the source of the error!\n";
        }
        
        // Check beneficiary audit table
        echo "\nChecking sidecar_beneficiary_amend_audits table...\n";
        $beneficiaryExists = $pdo->query("SHOW TABLES LIKE 'sidecar_beneficiary_amend_audits'")->fetch();
        
        if ($beneficiaryExists) {
            echo "✅ sidecar_beneficiary_amend_audits table EXISTS\n";
            $beneficiaryColumns = $pdo->query("DESCRIBE sidecar_beneficiary_amend_audits")->fetchAll(PDO::FETCH_ASSOC);
            echo "Columns:\n";
            foreach ($beneficiaryColumns as $col) {
                echo "- {$col['Field']} ({$col['Type']})\n";
            }
        } else {
            echo "❌ sidecar_beneficiary_amend_audits table does NOT exist\n";
        }
        
    } else {
        echo "Non-MySQL database detected. Using generic queries...\n";
        // Add SQLite or other database checks here if needed
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "RECOMMENDED ACTIONS:\n";
    echo str_repeat("=", 50) . "\n";
    
    if (!$vendorSyncExists) {
        echo "1. Create the missing sidecar_vendor_sync_audits table\n";
        echo "2. Update ReportsController to handle missing table gracefully\n";
    }
    
    echo "3. Verify column names match between code and database\n";
    echo "4. Consider using conditional queries based on table existence\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
