<?php
$title = 'Agent Transfer - Sidecar Admin';
global $config;
ob_start();
?>

<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?= baseUrl() ?>">Dashboard</a></li>
                <li class="breadcrumb-item active">Agent Transfer</li>
            </ol>
        </nav>
        
        <h1 class="mb-4">
            <i class="bi bi-arrow-left-right"></i> Agent Transfer
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-cash-stack"></i> Transfer Form
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= baseUrl('agents/transfer') ?>" id="transfer_form">
                    <input type="hidden" name="_token" value="<?= generateCsrfToken() ?>">
                    
                    <!-- Agent Selection -->
                    <div class="mb-4">
                        <label for="agent_search" class="form-label">
                            Select Agent <span class="required">*</span>
                        </label>
                        <div class="position-relative">
                            <input type="text" 
                                   class="form-control" 
                                   id="agent_search" 
                                   placeholder="Search by name or email..."
                                   autocomplete="off">
                            <div id="agent_dropdown" class="dropdown-menu w-100" style="max-height: 200px; overflow-y: auto;"></div>
                        </div>
                        <input type="hidden" name="agent_user_id" id="agent_user_id" required>
                        <div class="form-text">Type to search for agents</div>
                        
                        <!-- Selected Agent Display -->
                        <div id="selected_agent" class="d-none mt-2">
                            <div class="alert alert-info">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong id="agent_name"></strong><br>
                                        <small id="agent_email" class="text-muted"></small>
                                    </div>
                                    <div class="text-end">
                                        <div class="h5 mb-0">
                                            <span id="agent_balance" class="balance-positive"></span>
                                        </div>
                                        <small class="text-muted">Current Balance</small>
                                    </div>
                                </div>
                                <button type="button" class="btn-close position-absolute top-0 end-0 m-2" id="clear_agent"></button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Transfer Direction -->
                    <div class="mb-4">
                        <label class="form-label">
                            Transfer Direction <span class="required">*</span>
                        </label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="direction" 
                                           id="agent_to_cash" value="AgentToNestblockCash" required>
                                    <label class="form-check-label" for="agent_to_cash">
                                        <i class="bi bi-arrow-right text-danger"></i>
                                        Agent → NestblockCash
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="direction" 
                                           id="cash_to_agent" value="NestblockCashToAgent" required>
                                    <label class="form-check-label" for="cash_to_agent">
                                        <i class="bi bi-arrow-left text-success"></i>
                                        NestblockCash → Agent
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="direction" 
                                           id="agent_to_wallet" value="AgentToNestblockWallet" required>
                                    <label class="form-check-label" for="agent_to_wallet">
                                        <i class="bi bi-arrow-right text-danger"></i>
                                        Agent → NestblockWallet
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="direction" 
                                           id="wallet_to_agent" value="NestblockWalletToAgent" required>
                                    <label class="form-check-label" for="wallet_to_agent">
                                        <i class="bi bi-arrow-left text-success"></i>
                                        NestblockWallet → Agent
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- send_amount and Currency -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <label for="send_amount" class="form-label">
                                Amount <span class="required">*</span>
                            </label>
                            <input type="number" 
                                   class="form-control" 
                                   id="send_amount" 
                                   name="send_amount" 
                                   step="0.01" 
                                   min="0.01" 
                                   required
                                   oninput="formatCurrency(this)">
                        </div>
                        <div class="col-md-4">
                            <label for="send_curr" class="form-label">Currency</label>
                            <select class="form-select" id="send_curr" name="send_curr">
                                <option value="<?= htmlspecialchars($config['business']['default_send_curr']) ?>" selected>
                                    <?= htmlspecialchars($config['business']['default_send_curr']) ?>
                                </option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Reason -->
                    <div class="mb-4">
                        <label for="reason" class="form-label">
                            Reason for Transfer <span class="required">*</span>
                        </label>
                        <textarea class="form-control" 
                                  id="reason" 
                                  name="reason" 
                                  rows="3" 
                                  minlength="5" 
                                  required
                                  placeholder="Please provide a detailed reason for this transfer..."></textarea>
                        <div class="form-text">Minimum 5 characters required</div>
                    </div>
                    
                    <!-- Preview Section -->
                    <div id="transfer_preview" class="d-none mb-4">
                        <div class="card bg-light">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-eye"></i> Transfer Preview
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Agent Account</h6>
                                        <div class="d-flex justify-content-between">
                                            <span>Before:</span>
                                            <span id="agent_before" class="balance-positive"></span>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span>After:</span>
                                            <span id="agent_after" class="fw-bold"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 id="counterparty_title">Counterparty Account</h6>
                                        <div class="d-flex justify-content-between">
                                            <span>Before:</span>
                                            <span id="counterparty_before" class="balance-positive"></span>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span>After:</span>
                                            <span id="counterparty_after" class="fw-bold"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-secondary" onclick="window.history.back()">
                            <i class="bi bi-arrow-left"></i> Back
                        </button>
                        <div>
                            <button type="button" class="btn btn-info me-2" id="preview_btn" disabled>
                                <i class="bi bi-eye"></i> Preview
                            </button>
                            <button type="submit" class="btn btn-primary" disabled id="submit_btn">
                                <i class="bi bi-check-circle"></i> Execute Transfer
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-info-circle"></i> System Accounts
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>NestblockCash</strong><br>
                            <small class="text-muted">ID: <?= $config['business']['nestblock_cash_user_id'] ?></small>
                        </div>
                        <div class="text-end">
                            <span id="nestblock_cash_balance" class="balance-positive">Loading...</span>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>NestblockWallet</strong><br>
                            <small class="text-muted">ID: <?= $config['business']['nestblock_wallet_user_id'] ?></small>
                        </div>
                        <div class="text-end">
                            <span id="nestblock_wallet_balance" class="balance-positive">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-shield-check"></i> Transfer Rules
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        All transfers are recorded in double-entry ledger
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        Vendor balances are updated automatically
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        Full audit trail is maintained
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-exclamation-triangle text-warning"></i>
                        Transfers cannot be reversed once executed
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php
$additionalJs = '
<script>
document.addEventListener("DOMContentLoaded", function() {
    const agentSearch = document.getElementById("agent_search");
    const agentDropdown = document.getElementById("agent_dropdown");
    const agentUserId = document.getElementById("agent_user_id");
    const selectedAgent = document.getElementById("selected_agent");
    const clearAgent = document.getElementById("clear_agent");
    const previewBtn = document.getElementById("preview_btn");
    const submitBtn = document.getElementById("submit_btn");
    const transferPreview = document.getElementById("transfer_preview");
    const form = document.getElementById("transfer_form");
    
    let searchTimeout;
    let selectedAgentData = null;
    let systemBalances = {};
    
    // Load system account balances
    loadSystemBalances();
    
    // Agent search functionality
    agentSearch.addEventListener("input", function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();
        
        if (query.length < 2) {
            agentDropdown.classList.remove("show");
            return;
        }
        
        searchTimeout = setTimeout(() => searchAgents(query), 300);
    });
    
    // Clear agent selection
    clearAgent.addEventListener("click", function() {
        clearAgentSelection();
    });
    
    // Preview button
    previewBtn.addEventListener("click", function() {
        generatePreview();
    });
    
    // Form validation
    form.addEventListener("input", validateForm);
    form.addEventListener("change", validateForm);
    
    function searchAgents(query) {
        apiRequest(`/api/agents/search?q=${encodeURIComponent(query)}`)
            .then(agents => {
                displayAgentResults(agents);
            })
            .catch(error => {
                console.error("Agent search error:", error);
                agentDropdown.innerHTML = `<div class="dropdown-item text-danger">Error: ${error.message}</div>`;
                agentDropdown.classList.add("show");
            });
    }
    
    function displayAgentResults(agents) {
        if (agents.length === 0) {
            agentDropdown.innerHTML = \'<div class="dropdown-item text-muted">No agents found</div>\';
        } else {
            agentDropdown.innerHTML = agents.map(agent => `
                <div class="dropdown-item agent-option" data-agent=\'${JSON.stringify(agent)}\' style="cursor: pointer;">
                    <div class="d-flex justify-content-between">
                        <div>
                            <strong>${agent.username}</strong><br>
                            <small class="text-muted">${agent.email}</small>
                        </div>
                        <div class="text-end">
                            <span class="balance-positive">${formatCurrency(agent.balance)}</span>
                        </div>
                    </div>
                </div>
            `).join("");
            
            // Add click handlers
            document.querySelectorAll(".agent-option").forEach(option => {
                option.addEventListener("click", function() {
                    selectAgent(JSON.parse(this.dataset.agent));
                });
            });
        }
        
        agentDropdown.classList.add("show");
    }
    
    function selectAgent(agent) {
        selectedAgentData = agent;
        agentUserId.value = agent.id;
        agentSearch.value = agent.name;
        
        document.getElementById("agent_name").textContent = agent.name;
        document.getElementById("agent_email").textContent = agent.email;
        document.getElementById("agent_balance").textContent = formatCurrency(agent.balance);
        
        selectedAgent.classList.remove("d-none");
        agentDropdown.classList.remove("show");
        
        validateForm();
    }
    
    function clearAgentSelection() {
        selectedAgentData = null;
        agentUserId.value = "";
        agentSearch.value = "";
        selectedAgent.classList.add("d-none");
        transferPreview.classList.add("d-none");
        validateForm();
    }
    
    function loadSystemBalances() {
        const cashUserId = ' . $config['business']['nestblock_cash_user_id'] . ';
        const walletUserId = ' . $config['business']['nestblock_wallet_user_id'] . ';
        
        Promise.all([
            apiRequest(`/api/agents/${cashUserId}/balance`),
            apiRequest(`/api/agents/${walletUserId}/balance`)
        ]).then(([cashData, walletData]) => {
            systemBalances.cash = parseFloat(cashData.balance);
            systemBalances.wallet = parseFloat(walletData.balance);
            
            document.getElementById("nestblock_cash_balance").textContent = formatCurrency(cashData.balance);
            document.getElementById("nestblock_wallet_balance").textContent = formatCurrency(walletData.balance);
        }).catch(error => {
            console.error("System balance error:", error);
            document.getElementById("nestblock_cash_balance").textContent = "Error";
            document.getElementById("nestblock_wallet_balance").textContent = "Error";
        });
    }
    
    function validateForm() {
        const hasAgent = !!selectedAgentData;
        const hasDirection = document.querySelector(\'input[name="direction"]:checked\');
        const hassend_amount = document.getElementById("send_amount").value && parseFloat(document.getElementById("send_amount").value) > 0;
        const hasReason = document.getElementById("reason").value.trim().length >= 5;
        
        const isValid = hasAgent && hasDirection && hassend_amount && hasReason;
        
        previewBtn.disabled = !isValid;
        submitBtn.disabled = !isValid;
    }
    
    function generatePreview() {
        const direction = document.querySelector(\'input[name="direction"]:checked\').value;
        const send_amount = parseFloat(document.getElementById("send_amount").value);
        
        const agentBefore = selectedAgentData.balance;
        let agentAfter, counterpartyBefore, counterpartyAfter, counterpartyTitle;
        
        if (direction.includes("ToNestblockCash")) {
            counterpartyBefore = systemBalances.cash;
            counterpartyTitle = "NestblockCash Account";
        } else {
            counterpartyBefore = systemBalances.wallet;
            counterpartyTitle = "NestblockWallet Account";
        }
        
        if (direction.startsWith("Agent")) {
            // Agent sending money
            agentAfter = agentBefore - send_amount;
            counterpartyAfter = counterpartyBefore + send_amount;
        } else {
            // Agent receiving money
            agentAfter = agentBefore + send_amount;
            counterpartyAfter = counterpartyBefore - send_amount;
        }
        
        // Update preview
        document.getElementById("agent_before").textContent = formatCurrency(agentBefore);
        document.getElementById("agent_after").textContent = formatCurrency(agentAfter);
        document.getElementById("agent_after").className = agentAfter >= agentBefore ? "fw-bold balance-positive" : "fw-bold balance-negative";
        
        document.getElementById("counterparty_title").textContent = counterpartyTitle;
        document.getElementById("counterparty_before").textContent = formatCurrency(counterpartyBefore);
        document.getElementById("counterparty_after").textContent = formatCurrency(counterpartyAfter);
        document.getElementById("counterparty_after").className = counterpartyAfter >= counterpartyBefore ? "fw-bold balance-positive" : "fw-bold balance-negative";
        
        transferPreview.classList.remove("d-none");
        
        // Check for insufficient funds
        if (agentAfter < 0 || counterpartyAfter < 0) {
            transferPreview.querySelector(".card").className = "card border-danger";
            submitBtn.disabled = true;
            submitBtn.innerHTML = \'<i class="bi bi-exclamation-triangle"></i> Insufficient Funds\';
        } else {
            transferPreview.querySelector(".card").className = "card bg-light";
            submitBtn.disabled = false;
            submitBtn.innerHTML = \'<i class="bi bi-check-circle"></i> Execute Transfer\';
        }
    }
    
    function formatCurrency(send_amount, send_curr = "USD") {
        return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: send_curr
        }).format(send_amount);
    }
    
    // Hide dropdown when clicking outside
    document.addEventListener("click", function(e) {
        if (!agentSearch.contains(e.target) && !agentDropdown.contains(e.target)) {
            agentDropdown.classList.remove("show");
        }
    });
});
</script>
';

$content = ob_get_clean();
include __DIR__ . '/layout.php';
?>
