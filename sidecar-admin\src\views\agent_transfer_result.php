<?php
$title = 'Transfer Result - Sidecar Admin';
ob_start();
?>

<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?= baseUrl() ?>">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="<?= baseUrl('agents/transfer') ?>">Agent Transfer</a></li>
                <li class="breadcrumb-item active">Result</li>
            </ol>
        </nav>
        
        <h1 class="mb-4">
            <i class="bi bi-check-circle text-success"></i> Transfer Completed
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="bi bi-check-circle"></i> Transfer Successful
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <h6><i class="bi bi-info-circle"></i> Transaction Details</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Transaction ID:</strong><br>
                            <code><?= htmlspecialchars($transfer['tx_id']) ?></code>
                        </div>
                        <div class="col-md-6">
                            <strong>Processed At:</strong><br>
                            <?= date('M j, Y g:i:s A', strtotime($transfer['created_at'])) ?>
                        </div>
                    </div>
                </div>
                
                <h6 class="mb-3">Transfer Summary</h6>
                <div class="row mb-4">
                    <div class="col-md-3">
                        <strong>Direction:</strong><br>
                        <?php
                        $directionDisplay = str_replace(['To', 'Agent', 'Nestblock'], [' → ', 'Agent', 'Nestblock'], $transfer['direction']);
                        echo htmlspecialchars($directionDisplay);
                        ?>
                    </div>
                    <div class="col-md-3">
                        <strong>send_amount:</strong><br>
                        <span class="h5 text-primary">
                            <?= formatCurrency($transfer['send_amount'], $transfer['send_curr']) ?>
                        </span>
                    </div>
                    <div class="col-md-3">
                        <strong>Agent ID:</strong><br>
                        #<?= htmlspecialchars($transfer['agent_user_id']) ?>
                    </div>
                    <div class="col-md-3">
                        <strong>Counterparty:</strong><br>
                        <?= htmlspecialchars($transfer['counterparty_type']) ?>
                    </div>
                </div>
                
                <h6 class="mb-3">Balance Changes</h6>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Agent Account</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Before:</span>
                                    <span class="balance-positive">
                                        <?= formatCurrency($transfer['agent_balance_before'], $transfer['send_curr']) ?>
                                    </span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Change:</span>
                                    <span class="<?= ($transfer['agent_balance_after'] - $transfer['agent_balance_before']) >= 0 ? 'text-success' : 'text-danger' ?>">
                                        <?php
                                        $change = $transfer['agent_balance_after'] - $transfer['agent_balance_before'];
                                        echo ($change >= 0 ? '+' : '') . formatCurrency($change, $transfer['send_curr']);
                                        ?>
                                    </span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <strong>After:</strong>
                                    <strong class="<?= $transfer['agent_balance_after'] >= 0 ? 'balance-positive' : 'balance-negative' ?>">
                                        <?= formatCurrency($transfer['agent_balance_after'], $transfer['send_curr']) ?>
                                    </strong>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><?= htmlspecialchars($transfer['counterparty_type']) ?> Account</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Before:</span>
                                    <span class="balance-positive">
                                        <?= formatCurrency($transfer['counterparty_balance_before'], $transfer['send_curr']) ?>
                                    </span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Change:</span>
                                    <span class="<?= ($transfer['counterparty_balance_after'] - $transfer['counterparty_balance_before']) >= 0 ? 'text-success' : 'text-danger' ?>">
                                        <?php
                                        $change = $transfer['counterparty_balance_after'] - $transfer['counterparty_balance_before'];
                                        echo ($change >= 0 ? '+' : '') . formatCurrency($change, $transfer['send_curr']);
                                        ?>
                                    </span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <strong>After:</strong>
                                    <strong class="<?= $transfer['counterparty_balance_after'] >= 0 ? 'balance-positive' : 'balance-negative' ?>">
                                        <?= formatCurrency($transfer['counterparty_balance_after'], $transfer['send_curr']) ?>
                                    </strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h6 class="mb-3">Ledger Entries</h6>
                <div class="table-responsive mb-4">
                    <table class="table table-bordered table-sm">
                        <thead class="table-light">
                            <tr>
                                <th>Account</th>
                                <th>Direction</th>
                                <th>send_amount</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            // Determine posting based on direction
                            $postingRules = getPostingRules($transfer['direction']);
                            ?>
                            <tr>
                                <td>
                                    <?= $postingRules['debit'] === 'Agent' ? 'Agent' : htmlspecialchars($transfer['counterparty_type']) ?>
                                </td>
                                <td>
                                    <span class="badge bg-info">Debit</span>
                                </td>
                                <td><?= formatCurrency($transfer['send_amount'], $transfer['send_curr']) ?></td>
                                <td><?= htmlspecialchars($transfer['direction']) ?></td>
                            </tr>
                            <tr>
                                <td>
                                    <?= $postingRules['credit'] === 'Agent' ? 'Agent' : htmlspecialchars($transfer['counterparty_type']) ?>
                                </td>
                                <td>
                                    <span class="badge bg-warning">Credit</span>
                                </td>
                                <td><?= formatCurrency($transfer['send_amount'], $transfer['send_curr']) ?></td>
                                <td><?= htmlspecialchars($transfer['direction']) ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <h6 class="mb-3">Transfer Details</h6>
                <div class="audit-entry">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Executed By:</strong><br>
                            <?= htmlspecialchars($transfer['created_by']) ?>
                        </div>
                        <div class="col-md-6">
                            <strong>Timestamp:</strong><br>
                            <?= date('M j, Y g:i:s A', strtotime($transfer['created_at'])) ?>
                        </div>
                    </div>
                    <div class="mt-3">
                        <strong>Reason:</strong><br>
                        <div class="bg-light p-3 rounded">
                            <?= nl2br(htmlspecialchars($transfer['reason'])) ?>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4 d-flex justify-content-between">
                    <a href="<?= baseUrl('agents/transfer') ?>" class="btn btn-outline-primary">
                        <i class="bi bi-plus-circle"></i> New Transfer
                    </a>
                    <div>
                        <button type="button" class="btn btn-outline-secondary me-2" onclick="window.print()">
                            <i class="bi bi-printer"></i> Print
                        </button>
                        <a href="<?= baseUrl('reports/audits?tx_id=' . urlencode($transfer['tx_id'])) ?>" class="btn btn-info">
                            <i class="bi bi-file-text"></i> View in Audit Log
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-info-circle"></i> What Happened?
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-3">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Balances Updated</strong><br>
                        <small class="text-muted">Both account balances have been updated in the vendor system</small>
                    </li>
                    <li class="mb-3">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Ledger Entries Created</strong><br>
                        <small class="text-muted">Double-entry bookkeeping records have been created</small>
                    </li>
                    <li class="mb-3">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Vendor Transactions</strong><br>
                        <small class="text-muted">Transaction records have been inserted into the vendor system</small>
                    </li>
                    <li class="mb-3">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Audit Trail</strong><br>
                        <small class="text-muted">Complete audit record has been created for compliance</small>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-shield-check"></i> Compliance Note
                </h6>
            </div>
            <div class="card-body">
                <p class="small text-muted mb-0">
                    This transfer has been recorded with transaction ID 
                    <code><?= htmlspecialchars($transfer['tx_id']) ?></code> 
                    and is permanently stored in the audit trail. The transfer cannot be reversed 
                    through this system.
                </p>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-exclamation-triangle"></i> Important
                </h6>
            </div>
            <div class="card-body">
                <p class="small mb-0">
                    <strong>This transfer is final.</strong> If you need to reverse this transaction, 
                    you must create a new transfer in the opposite direction with appropriate documentation.
                </p>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .card-header, nav, .col-lg-4 {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .audit-entry {
        border-left: 3px solid #000 !important;
    }
}
</style>

<?php
$content = ob_get_clean();
include __DIR__ . '/layout.php';
?>
