#!/usr/bin/env php
<?php
/**
 * Reconciliation Job
 * Runs nightly to check sidecar vs vendor balance consistency
 */

// Include bootstrap
require_once __DIR__ . '/../src/bootstrap.php';

// Ensure we're running from command line
if (php_sapi_name() !== 'cli') {
    die('This script can only be run from the command line.');
}

echo "Sidecar Admin Reconciliation Job\n";
echo "================================\n";
echo "Started at: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // Create reports controller instance
    require_once __DIR__ . '/../src/controllers/ReportsController.php';
    $reportsController = new ReportsController();
    
    // Perform reconciliation
    echo "Running reconciliation checks...\n";
    $reconciliation = $reportsController->performReconciliation($pdo, $config);
    
    // Display results
    echo "\nReconciliation Results:\n";
    echo "======================\n";
    
    if (isset($reconciliation['error'])) {
        echo "ERROR: " . $reconciliation['error'] . "\n";
        exit(1);
    }
    
    $summary = $reconciliation['summary'];
    echo "Total Checks: {$summary['total_checks']}\n";
    echo "Passed: {$summary['passed_checks']}\n";
    echo "Failed: {$summary['failed_checks']}\n";
    
    if ($summary['failed_checks'] === 0) {
        echo "\n✅ All reconciliation checks PASSED\n";
        $exitCode = 0;
    } else {
        echo "\n❌ Reconciliation checks FAILED\n";
        $exitCode = 1;
        
        echo "\nDiscrepancies:\n";
        echo "--------------\n";
        foreach ($reconciliation['discrepancies'] as $i => $discrepancy) {
            echo ($i + 1) . ". " . ucfirst(str_replace('_', ' ', $discrepancy['type'])) . "\n";
            echo "   " . $discrepancy['description'] . "\n";
            if (isset($discrepancy['tx_id'])) {
                echo "   TX ID: " . $discrepancy['tx_id'] . "\n";
            }
            echo "\n";
        }
    }
    
    // Detailed balance report
    echo "\nBalance Report:\n";
    echo "===============\n";
    
    echo "\nLedger Balance Check: " . ($reconciliation['ledger_balance'] ? "PASS" : "FAIL") . "\n";
    
    if (!empty($reconciliation['agent_balances'])) {
        echo "\nAgent Balances:\n";
        foreach ($reconciliation['agent_balances'] as $agentId => $balance) {
            $status = $balance['status'] === 'match' ? "✅" : "❌";
            echo "  Agent #{$agentId}: {$status} ";
            echo "Sidecar: " . number_format($balance['sidecar'], 2) . ", ";
            echo "Vendor: " . number_format($balance['vendor'], 2);
            if ($balance['status'] === 'mismatch') {
                echo " (Diff: " . number_format($balance['difference'], 2) . ")";
            }
            echo "\n";
        }
    }
    
    if (!empty($reconciliation['system_balances'])) {
        echo "\nSystem Account Balances:\n";
        foreach ($reconciliation['system_balances'] as $accountType => $balance) {
            $status = $balance['status'] === 'match' ? "✅" : "❌";
            echo "  {$accountType}: {$status} ";
            echo "Sidecar: " . number_format($balance['sidecar'], 2) . ", ";
            echo "Vendor: " . number_format($balance['vendor'], 2);
            if ($balance['status'] === 'mismatch') {
                echo " (Diff: " . number_format($balance['difference'], 2) . ")";
            }
            echo "\n";
        }
    }
    
    // Generate CSV report if requested
    if (in_array('--csv', $argv) || in_array('-c', $argv)) {
        $csvFile = generateCsvReport($reconciliation);
        echo "\nCSV report generated: {$csvFile}\n";
    }
    
    // Generate HTML report if requested
    if (in_array('--html', $argv) || in_array('-h', $argv)) {
        $htmlFile = generateHtmlReport($reconciliation);
        echo "\nHTML report generated: {$htmlFile}\n";
    }
    
    echo "\nCompleted at: " . date('Y-m-d H:i:s') . "\n";
    
    // Log the reconciliation result
    logMessage('info', 'Reconciliation job completed', [
        'total_checks' => $summary['total_checks'],
        'passed_checks' => $summary['passed_checks'],
        'failed_checks' => $summary['failed_checks'],
        'discrepancies_count' => count($reconciliation['discrepancies'])
    ]);
    
    exit($exitCode);
    
} catch (Exception $e) {
    echo "FATAL ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    
    logMessage('error', 'Reconciliation job failed', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
    
    exit(1);
}

/**
 * Generate CSV report
 */
function generateCsvReport($reconciliation) {
    $timestamp = date('Y-m-d_H-i-s');
    $filename = __DIR__ . "/../logs/reconciliation_{$timestamp}.csv";
    
    $fp = fopen($filename, 'w');
    
    // Header
    fputcsv($fp, ['Reconciliation Report', date('Y-m-d H:i:s')]);
    fputcsv($fp, []);
    
    // Summary
    fputcsv($fp, ['Summary']);
    fputcsv($fp, ['Total Checks', $reconciliation['summary']['total_checks']]);
    fputcsv($fp, ['Passed Checks', $reconciliation['summary']['passed_checks']]);
    fputcsv($fp, ['Failed Checks', $reconciliation['summary']['failed_checks']]);
    fputcsv($fp, []);
    
    // Agent balances
    if (!empty($reconciliation['agent_balances'])) {
        fputcsv($fp, ['Agent Balances']);
        fputcsv($fp, ['Agent ID', 'Sidecar Balance', 'Vendor Balance', 'Status', 'Difference']);
        foreach ($reconciliation['agent_balances'] as $agentId => $balance) {
            fputcsv($fp, [
                $agentId,
                number_format($balance['sidecar'], 2),
                number_format($balance['vendor'], 2),
                $balance['status'],
                isset($balance['difference']) ? number_format($balance['difference'], 2) : '0.00'
            ]);
        }
        fputcsv($fp, []);
    }
    
    // System balances
    if (!empty($reconciliation['system_balances'])) {
        fputcsv($fp, ['System Account Balances']);
        fputcsv($fp, ['Account Type', 'Sidecar Balance', 'Vendor Balance', 'Status', 'Difference']);
        foreach ($reconciliation['system_balances'] as $accountType => $balance) {
            fputcsv($fp, [
                $accountType,
                number_format($balance['sidecar'], 2),
                number_format($balance['vendor'], 2),
                $balance['status'],
                isset($balance['difference']) ? number_format($balance['difference'], 2) : '0.00'
            ]);
        }
        fputcsv($fp, []);
    }
    
    // Discrepancies
    if (!empty($reconciliation['discrepancies'])) {
        fputcsv($fp, ['Discrepancies']);
        fputcsv($fp, ['Type', 'Description', 'TX ID']);
        foreach ($reconciliation['discrepancies'] as $discrepancy) {
            fputcsv($fp, [
                $discrepancy['type'],
                $discrepancy['description'],
                $discrepancy['tx_id'] ?? ''
            ]);
        }
    }
    
    fclose($fp);
    return $filename;
}

/**
 * Generate HTML report
 */
function generateHtmlReport($reconciliation) {
    $timestamp = date('Y-m-d_H-i-s');
    $filename = __DIR__ . "/../logs/reconciliation_{$timestamp}.html";
    
    $html = '<!DOCTYPE html>
<html>
<head>
    <title>Reconciliation Report - ' . date('Y-m-d H:i:s') . '</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .pass { color: green; }
        .fail { color: red; }
        .summary { background-color: #f9f9f9; padding: 15px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Sidecar Admin Reconciliation Report</h1>
    <p><strong>Generated:</strong> ' . date('Y-m-d H:i:s') . '</p>
    
    <div class="summary">
        <h2>Summary</h2>
        <p><strong>Total Checks:</strong> ' . $reconciliation['summary']['total_checks'] . '</p>
        <p><strong>Passed:</strong> <span class="pass">' . $reconciliation['summary']['passed_checks'] . '</span></p>
        <p><strong>Failed:</strong> <span class="fail">' . $reconciliation['summary']['failed_checks'] . '</span></p>
    </div>';
    
    if (!empty($reconciliation['agent_balances'])) {
        $html .= '<h2>Agent Balances</h2>
        <table>
            <tr><th>Agent ID</th><th>Sidecar Balance</th><th>Vendor Balance</th><th>Status</th><th>Difference</th></tr>';
        
        foreach ($reconciliation['agent_balances'] as $agentId => $balance) {
            $statusClass = $balance['status'] === 'match' ? 'pass' : 'fail';
            $html .= '<tr>
                <td>' . htmlspecialchars($agentId) . '</td>
                <td>' . number_format($balance['sidecar'], 2) . '</td>
                <td>' . number_format($balance['vendor'], 2) . '</td>
                <td class="' . $statusClass . '">' . ucfirst($balance['status']) . '</td>
                <td>' . (isset($balance['difference']) ? number_format($balance['difference'], 2) : '0.00') . '</td>
            </tr>';
        }
        
        $html .= '</table>';
    }
    
    if (!empty($reconciliation['system_balances'])) {
        $html .= '<h2>System Account Balances</h2>
        <table>
            <tr><th>Account Type</th><th>Sidecar Balance</th><th>Vendor Balance</th><th>Status</th><th>Difference</th></tr>';
        
        foreach ($reconciliation['system_balances'] as $accountType => $balance) {
            $statusClass = $balance['status'] === 'match' ? 'pass' : 'fail';
            $html .= '<tr>
                <td>' . htmlspecialchars($accountType) . '</td>
                <td>' . number_format($balance['sidecar'], 2) . '</td>
                <td>' . number_format($balance['vendor'], 2) . '</td>
                <td class="' . $statusClass . '">' . ucfirst($balance['status']) . '</td>
                <td>' . (isset($balance['difference']) ? number_format($balance['difference'], 2) : '0.00') . '</td>
            </tr>';
        }
        
        $html .= '</table>';
    }
    
    if (!empty($reconciliation['discrepancies'])) {
        $html .= '<h2>Discrepancies</h2>
        <table>
            <tr><th>Type</th><th>Description</th><th>TX ID</th></tr>';
        
        foreach ($reconciliation['discrepancies'] as $discrepancy) {
            $html .= '<tr>
                <td>' . htmlspecialchars(ucfirst(str_replace('_', ' ', $discrepancy['type']))) . '</td>
                <td>' . htmlspecialchars($discrepancy['description']) . '</td>
                <td>' . htmlspecialchars($discrepancy['tx_id'] ?? '') . '</td>
            </tr>';
        }
        
        $html .= '</table>';
    }
    
    $html .= '</body></html>';
    
    file_put_contents($filename, $html);
    return $filename;
}
