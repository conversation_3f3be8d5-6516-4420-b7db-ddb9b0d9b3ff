<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($title ?? 'Sidecar Admin') ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #f8f9fa;
        }
        .main-content {
            min-height: calc(100vh - 56px);
        }
        .flash-messages {
            position: fixed;
            top: 70px;
            right: 20px;
            z-index: 1050;
            max-width: 400px;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .btn-group-sm > .btn, .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        .table-sm th, .table-sm td {
            padding: 0.3rem;
        }
        .form-label {
            font-weight: 500;
        }
        .required {
            color: #dc3545;
        }
        .balance-positive {
            color: #198754;
            font-weight: 500;
        }
        .balance-negative {
            color: #dc3545;
            font-weight: 500;
        }
        .audit-entry {
            border-left: 3px solid #0d6efd;
            padding-left: 1rem;
        }
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="<?= baseUrl() ?>">
                <i class="bi bi-shield-check"></i> Sidecar Admin
            </a>
            
            <?php if (isLoggedIn()): ?>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="<?= baseUrl() ?>">
                                <i class="bi bi-house"></i> Dashboard
                            </a>
                        </li>
                        
                        <?php if (hasRole(['Admin', 'Compliance'])): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-lines-fill"></i> Beneficiaries
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?= baseUrl('beneficiaries/amend') ?>">Amend Details</a></li>
                            </ul>
                        </li>
                        <?php endif; ?>
                        
                        <?php if (hasRole(['Admin', 'Finance'])): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-cash-stack"></i> Transfers
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?= baseUrl('agents/transfer') ?>">Agent Transfers</a></li>
                            </ul>
                        </li>
                        <?php endif; ?>
                        
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-file-text"></i> Reports
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?= baseUrl('reports/audits') ?>">Audit Trail</a></li>
                                <li><a class="dropdown-item" href="<?= baseUrl('reports/statements') ?>">Statements</a></li>
                            </ul>
                        </li>
                    </ul>
                    
                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle"></i> 
                                <?= htmlspecialchars(getCurrentUser()['email']) ?>
                                <span class="badge bg-secondary"><?= htmlspecialchars(getCurrentUser()['role']) ?></span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="<?= baseUrl('profile') ?>">Profile</a></li>
                                <?php if (hasRole(['Admin'])): ?>
                                <li><a class="dropdown-item" href="<?= baseUrl('users') ?>">Manage Users</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="<?= baseUrl('logout') ?>" class="d-inline">
                                        <input type="hidden" name="_token" value="<?= generateCsrfToken() ?>">
                                        <button type="submit" class="dropdown-item">
                                            <i class="bi bi-box-arrow-right"></i> Logout
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
    </nav>
    
    <!-- Flash Messages -->
    <div class="flash-messages">
        <?php foreach (getFlashMessages() as $type => $message): ?>
            <div class="alert alert-<?= $type === 'error' ? 'danger' : $type ?> alert-dismissible fade show" role="alert">
                <?= htmlspecialchars($message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endforeach; ?>
    </div>
    
    <!-- Main Content -->
    <div class="container-fluid">
        <?php if (isset($content)): ?>
            <?= $content ?>
        <?php endif; ?>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Common JavaScript -->
    <script>
        // Auto-hide flash messages after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.flash-messages .alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // Confirm dangerous actions
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('confirm-action')) {
                if (!confirm('Are you sure you want to perform this action?')) {
                    e.preventDefault();
                }
            }
        });
        
        // Loading state for forms
        document.addEventListener('submit', function(e) {
            const form = e.target;
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Processing...';
                form.classList.add('loading');
            }
        });
        
        // Format send_curr inputs
        function formatCurrency(input) {
            let value = input.value.replace(/[^\d.]/g, '');
            if (value.includes('.')) {
                const parts = value.split('.');
                value = parts[0] + '.' + parts[1].substring(0, 2);
            }
            input.value = value;
        }
        
        // CSRF token for AJAX requests
        const csrfToken = '<?= generateCsrfToken() ?>';
        
        // Common AJAX helper
        function apiRequest(url, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                }
            };
            
            return fetch(url, { ...defaultOptions, ...options })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                });
        }
    </script>
    
    <?php if (isset($additionalJs)): ?>
        <?= $additionalJs ?>
    <?php endif; ?>
</body>
</html>
