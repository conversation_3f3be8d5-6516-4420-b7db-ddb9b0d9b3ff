<?php
/**
 * Sidecar Admin Bootstrap
 * Initializes the application environment and dependencies
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', getenv('APP_ENV') === 'local' ? '1' : '0');

// Set timezone
date_default_timezone_set('UTC');

// Load environment variables from .env file
function loadEnv($path) {
    if (!file_exists($path)) {
        return;
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue; // Skip comments
        }
        
        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);
        
        // Remove quotes if present
        if (preg_match('/^"(.*)"$/', $value, $matches)) {
            $value = $matches[1];
        } elseif (preg_match("/^'(.*)'$/", $value, $matches)) {
            $value = $matches[1];
        }
        
        if (!array_key_exists($name, $_ENV)) {
            $_ENV[$name] = $value;
            putenv("$name=$value");
        }
    }
}

// Load .env file
$envPath = __DIR__ . '/../.env';
loadEnv($envPath);

// Helper function to get environment variables with defaults
function env($key, $default = null) {
    $value = $_ENV[$key] ?? getenv($key);
    if ($value === false) {
        return $default;
    }
    
    // Convert string booleans
    if (strtolower($value) === 'true') return true;
    if (strtolower($value) === 'false') return false;
    if (strtolower($value) === 'null') return null;
    
    return $value;
}

// Configuration array
$config = [
    'app' => [
        'name' => env('APP_NAME', 'Sidecar Admin'),
        'env' => env('APP_ENV', 'production'),
        'url' => env('APP_URL', 'http://localhost:8080'),
        'debug' => env('APP_ENV') === 'local',
    ],
    'database' => [
        'connection' => env('DB_CONNECTION', 'mysql'),
        'database' => env('DB_DATABASE', __DIR__ . '/../test.db'),
        'host' => env('DB_HOST', '127.0.0.1'),
        'port' => env('DB_PORT', 3306),
        'name' => env('DB_NAME', 'vendor_db'),
        'user' => env('DB_USER', 'sidecar_ops'),
        'pass' => env('DB_PASS', ''),
        'charset' => 'utf8mb4',
    ],
    'session' => [
        'secret' => env('SESSION_SECRET', 'change_me'),
        'lifetime' => (int)env('SESSION_LIFETIME', 3600),
        'name' => 'SIDECAR_SESSION',
    ],
    'vendor' => [
        'tables' => [
            'users' => env('VENDOR_TABLE_USERS', 'users'),
            'remittances' => env('VENDOR_TABLE_REMITTANCES', 'remittances'),
            'beneficiaries' => env('VENDOR_TABLE_BENEFICIARIES', 'beneficiaries'),
            'transactions' => env('VENDOR_TABLE_TRANSACTIONS', 'transactions'),
        ],
        'columns' => [
            'user_id' => env('VENDOR_COL_USER_ID', 'id'),
            'user_balance' => env('VENDOR_COL_USER_BALANCE', 'balance'),
            'remittance_id' => env('VENDOR_COL_REMITTANCE_ID', 'id'),
            'remittance_status' => env('VENDOR_COL_REMITTANCE_STATUS', 'status'),
            'remittance_beneficiary_id' => env('VENDOR_COL_REMITTANCE_BENEFICIARY_ID', 'beneficiary_id'),
            'beneficiary_id' => env('VENDOR_COL_BENEFICIARY_ID', 'id'),
            'beneficiary_name' => env('VENDOR_COL_BENEFICIARY_NAME', 'name'),
            'beneficiary_phone' => env('VENDOR_COL_BENEFICIARY_PHONE', 'phone'),
        ],
    ],
    'business' => [
        'remittance_disallowed_statuses' => explode(',', env('REMITTANCE_DISALLOWED_STATUSES', 'PaidOut,Cancelled')),
        'default_send_curr' => env('DEFAULT_CURRENCY', 'USD'),
        'nestblock_cash_user_id' => (int)env('NESTBLOCK_CASH_USER_ID', 1001),
        'nestblock_wallet_user_id' => (int)env('NESTBLOCK_WALLET_USER_ID', 1002),
        'trx_prefix' => env('TRX_PREFIX', 'SC'),
        'set_final_balance_in_vendor_transactions' => env('SET_FINAL_BALANCE_IN_VENDOR_TRANSACTIONS', 'true') === 'true',
    ],
    'security' => [
        'csrf_token_name' => env('CSRF_TOKEN_NAME', '_token'),
        'rate_limit_requests' => (int)env('RATE_LIMIT_REQUESTS', 100),
        'rate_limit_window' => (int)env('RATE_LIMIT_WINDOW', 3600),
    ],
];

// Make config globally available
$GLOBALS['config'] = $config;

// Configure session
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_samesite', 'Lax');
ini_set('session.use_strict_mode', 1);
ini_set('session.name', $config['session']['name']);

if (env('APP_ENV') === 'production') {
    ini_set('session.cookie_secure', 1);
}

// Include other core files
require_once __DIR__ . '/db.php';
require_once __DIR__ . '/helpers.php';
require_once __DIR__ . '/auth.php';
require_once __DIR__ . '/middleware.php';

// Initialize database connection
try {
    $pdo = createDatabaseConnection($config['database']);
    $GLOBALS['pdo'] = $pdo;
} catch (Exception $e) {
    if ($config['app']['debug']) {
        die('Database connection failed: ' . $e->getMessage());
    } else {
        die('Database connection failed. Please check configuration.');
    }
}
