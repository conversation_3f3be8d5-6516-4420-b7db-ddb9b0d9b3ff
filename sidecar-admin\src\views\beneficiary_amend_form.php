<?php
$title = 'Amend Beneficiary - Sidecar Admin';
$invoiceNo = $_GET['invoice_no'] ?? '';
ob_start();
?>

<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?= baseUrl() ?>">Dashboard</a></li>
                <li class="breadcrumb-item active">Amend Beneficiary</li>
            </ol>
        </nav>

        <h1 class="mb-4">
            <i class="bi bi-person-lines-fill"></i> Amend Beneficiary Details
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-pencil-square"></i> Amendment Form
                </h5>
            </div>
            <div class="card-body">
                <!-- Remittance Search -->
                <div class="mb-4">
                    <label for="invoice_search" class="form-label">
                        Search Remittance <span class="required">*</span>
                    </label>
                    <div class="input-group">
                        <input type="text"
                               class="form-control"
                               id="invoice_search"
                               placeholder="Enter invoice number or part of it"
                               value="<?= htmlspecialchars($invoiceNo) ?>"
                               autocomplete="off">
                        <button type="button" class="btn btn-outline-primary" id="search_btn">
                            <i class="bi bi-search"></i> Search
                        </button>
                    </div>
                    <div class="form-text">Enter the invoice number to find and load remittance details</div>

                    <!-- Search Results -->
                    <div id="search_results" class="mt-3 d-none">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Search Results</h6>
                            </div>
                            <div class="card-body">
                                <div id="results_list"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Remittance Details (hidden initially) -->
                <div id="remittance_details" class="d-none">
                    <div class="alert alert-info">
                        <h6><i class="bi bi-info-circle"></i> Remittance Information</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Invoice No:</strong> <span id="invoice_display"></span><br>
                                <strong>Status:</strong> <span id="remittance_status" class="badge"></span><br>
                            </div>
                            <div class="col-md-6">
                                <strong>send_amount:</strong> <span id="remittance_send_amount"></span><br>
                                <strong>Currency:</strong> <span id="remittance_send_curr"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Amendment Form -->
                    <form method="POST" action="<?= baseUrl('beneficiaries/amend') ?>" id="amendment_form">
                        <input type="hidden" name="_token" value="<?= generateCsrfToken() ?>">
                        <input type="hidden" name="invoice_no" id="form_invoice_no">

                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-muted">Current Details</h6>
                                <div class="mb-3">
                                    <label class="form-label">Current Name</label>
                                    <input type="text" class="form-control" id="current_name" readonly>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Current Email</label>
                                    <input type="email" class="form-control" id="current_email" readonly>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Current Phone</label>
                                    <input type="text" class="form-control" id="current_phone" readonly>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h6 class="text-muted">New Details</h6>
                                <div class="mb-3">
                                    <label for="name" class="form-label">
                                        New Name <span class="required">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="name"
                                           name="name"
                                           maxlength="255"
                                           required>
                                </div>
                                <div class="mb-3">
                                    <label for="email" class="form-label">
                                        New Email <span class=""></span>
                                    </label>
                                    <input type="email"
                                           class="form-control"
                                           id="email"
                                           name="email"
                                           maxlength="255"
                                           >
                                </div>
                                <div class="mb-3">
                                    <label for="phone" class="form-label">
                                        New Phone <span class="required">*</span>
                                    </label>
                                    <input type="tel"
                                           class="form-control"
                                           id="phone"
                                           name="phone"
                                           maxlength="50"
                                           required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="reason" class="form-label">
                                Reason for Amendment <span class="required">*</span>
                            </label>
                            <textarea class="form-control" 
                                      id="reason" 
                                      name="reason" 
                                      rows="3" 
                                      minlength="5" 
                                      required
                                      placeholder="Please provide a detailed reason for this amendment..."></textarea>
                            <div class="form-text">Minimum 5 characters required</div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-secondary" onclick="window.history.back()">
                                <i class="bi bi-arrow-left"></i> Back
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Submit Amendment
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-info-circle"></i> Amendment Guidelines
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        Only pending and processing remittances can be amended
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        All changes are logged with full audit trail
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        Reason is required for compliance purposes
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-exclamation-triangle text-warning"></i>
                        Changes cannot be undone once submitted
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-shield-check"></i> Security Notice
                </h6>
            </div>
            <div class="card-body">
                <p class="small text-muted mb-0">
                    This action will be recorded in the audit log with your user ID, 
                    timestamp, and IP address for security and compliance purposes.
                </p>
            </div>
        </div>
    </div>
</div>

<?php
$additionalJs = '
<script>
document.addEventListener("DOMContentLoaded", function() {
    const searchBtn = document.getElementById("search_btn");
    const invoiceInput = document.getElementById("invoice_search");
    const searchResults = document.getElementById("search_results");
    const resultsList = document.getElementById("results_list");
    const remittanceDetails = document.getElementById("remittance_details");
    const amendmentForm = document.getElementById("amendment_form");

    // Auto-search if invoice number is provided in URL
    if (invoiceInput.value) {
        searchRemittances();
    }

    searchBtn.addEventListener("click", searchRemittances);

    invoiceInput.addEventListener("keypress", function(e) {
        if (e.key === "Enter") {
            e.preventDefault();
            searchRemittances();
        }
    });

    // Real-time search as user types
    let searchTimeout;
    invoiceInput.addEventListener("input", function() {
        clearTimeout(searchTimeout);
        const query = invoiceInput.value.trim();

        if (query.length >= 2) {
            searchTimeout = setTimeout(() => {
                searchRemittances();
            }, 500);
        } else {
            searchResults.classList.add("d-none");
        }
    });

    function searchRemittances() {
        const query = invoiceInput.value.trim();

        if (query.length < 2) {
            alert("Please enter at least 2 characters to search");
            return;
        }

        searchBtn.disabled = true;
        searchBtn.innerHTML = \'<span class="spinner-border spinner-border-sm"></span> Searching...\';

        apiRequest(`/api/remittances/search?q=${encodeURIComponent(query)}`)
            .then(data => {
                displaySearchResults(data);
                searchResults.classList.remove("d-none");
            })
            .catch(error => {
                console.error("Search error:", error);
                alert("Error searching remittances: " + error.message);
                searchResults.classList.add("d-none");
            })
            .finally(() => {
                searchBtn.disabled = false;
                searchBtn.innerHTML = \'<i class="bi bi-search"></i> Search\';
            });
    }

    function displaySearchResults(results) {
        if (results.length === 0) {
            resultsList.innerHTML = \'<div class="text-muted">No remittances found matching your search.</div>\';
            return;
        }

        let html = \'<div class="list-group">\';
        results.forEach(result => {
            const statusColor = getStatusColor(result.status);
            html += `
                <button type="button" class="list-group-item list-group-item-action"
                        onclick="selectRemittance(\'${result.invoice}\')">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">Invoice: ${result.invoice}</h6>
                            <p class="mb-1"><strong>${result.recipient_name}</strong></p>
                            <small class="text-muted">${result.recipient_email} | ${result.recipient_contact_no}</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-${statusColor}">${result.status}</span><br>
                            <small class="text-muted">${formatCurrency(result.send_amount, result.send_curr)}</small>
                        </div>
                    </div>
                </button>
            `;
        });
        html += \'</div>\';

        resultsList.innerHTML = html;
    }

    window.selectRemittance = function(invoiceNo) {
        // Load full remittance details
        apiRequest(`/api/remittances/${encodeURIComponent(invoiceNo)}`)
            .then(data => {
                displayRemittanceDetails(data);
                remittanceDetails.classList.remove("d-none");
                searchResults.classList.add("d-none");

                // Update search input
                invoiceInput.value = invoiceNo;
            })
            .catch(error => {
                console.error("Load remittance error:", error);
                alert("Error loading remittance details: " + error.message);
            });
    }

    function displayRemittanceDetails(data) {
        // Display remittance info
        document.getElementById("invoice_display").textContent = data.invoice;
        document.getElementById("remittance_send_amount").textContent = formatCurrency(data.send_amount, data.send_curr);
        document.getElementById("remittance_send_curr").textContent = data.send_curr;

        const statusBadge = document.getElementById("remittance_status");
        statusBadge.textContent = data.status;
        statusBadge.className = `badge bg-${getStatusColor(data.status)}`;

        // Check if amendment is allowed
        const disallowedStatuses = ["PaidOut", "Cancelled", "Refunded", "Failed"];
        if (disallowedStatuses.includes(data.status)) {
            amendmentForm.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Amendment Not Allowed</strong><br>
                    Remittances with status "${data.status}" cannot be amended.
                </div>
            `;
            return;
        }

        // Populate current details
        document.getElementById("current_name").value = data.recipient_name;
        document.getElementById("current_email").value = data.recipient_email;
        document.getElementById("current_phone").value = data.recipient_contact_no;

        // Pre-populate new details with current values
        document.getElementById("name").value = data.recipient_name;
        document.getElementById("email").value = data.recipient_email;
        document.getElementById("phone").value = data.recipient_contact_no;

        // Set hidden form field
        document.getElementById("form_invoice_no").value = data.invoice;
    }
    
    function getStatusColor(status) {
        switch (status) {
            case "Pending": return "warning";
            case "Processing": return "info";
            case "PaidOut": return "success";
            case "Cancelled": return "danger";
            case "Failed": return "danger";
            default: return "secondary";
        }
    }
    
    function formatCurrency(send_amount, send_curr = "USD") {
        return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: send_curr
        }).format(send_amount);
    }
});
</script>
';

$content = ob_get_clean();
include __DIR__ . '/layout.php';
?>
