<?php
/**
 * Sidecar Admin - Main Entry Point
 */

// Include bootstrap
require_once __DIR__ . '/../src/bootstrap.php';

// Apply middleware
applyMiddleware();

// Include routes
require_once __DIR__ . '/../src/routes.php';

// Get current request
$method = $_SERVER['REQUEST_METHOD'];
$uri = $_SERVER['REQUEST_URI'];

// Remove base path if running in subdirectory
$basePath = dirname($_SERVER['SCRIPT_NAME']);
if ($basePath !== '/') {
    $uri = substr($uri, strlen($basePath));
}

// Ensure URI starts with /
if (!$uri || $uri[0] !== '/') {
    $uri = '/' . $uri;
}

try {
    // Dispatch request
    $router->dispatch($method, $uri);
} catch (Exception $e) {
    logMessage('error', 'Router exception: ' . $e->getMessage(), [
        'method' => $method,
        'uri' => $uri,
        'trace' => $e->getTraceAsString()
    ]);
    
    global $config;
    if ($config['app']['debug']) {
        echo "<h1>Router Error</h1>";
        echo "<p><b>Message:</b> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><b>Method:</b> $method</p>";
        echo "<p><b>URI:</b> " . htmlspecialchars($uri) . "</p>";
    } else {
        http_response_code(500);
        echo "<h1>Internal Server Error</h1>";
        echo "<p>An error occurred while processing your request.</p>";
    }
}
