<?php
/**
 * Middleware Functions
 */

/**
 * CSRF Protection Middleware
 */
function csrfMiddleware() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $token = $_POST['_token'] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? '';
        
        if (!verifyCsrfToken($token)) {
            http_response_code(403);
            die('CSRF token mismatch');
        }
    }
}

/**
 * Rate Limiting Middleware
 */
function rateLimitMiddleware() {
    $identifier = $_SERVER['REMOTE_ADDR'];
    
    // Use user ID if logged in
    $user = getCurrentUser();
    if ($user) {
        $identifier = 'user_' . $user['id'];
    }
    //Disable Rate Limit for Dev. 
    /*
    if (!checkRateLimit($identifier)) {
        http_response_code(429);
        header('Retry-After: 3600');
        die('Rate limit exceeded. Please try again later.');
    }
    */
}

/**
 * Security Headers Middleware
 */
function securityHeadersMiddleware() {
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: strict-origin-when-cross-origin');
    
    // Only set HSTS in production
    global $config;
    if ($config['app']['env'] === 'production') {
        header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
    }
}

/**
 * Content Type Middleware
 */
function contentTypeMiddleware() {
    if (!headers_sent()) {
        header('Content-Type: text/html; charset=UTF-8');
    }
}

/**
 * Session Cleanup Middleware
 */
function sessionCleanupMiddleware() {
    // Clean up expired sessions periodically (1% chance)
    if (rand(1, 100) === 1) {
        cleanupExpiredSessions();
    }
}

/**
 * Logging Middleware
 */
function loggingMiddleware() {
    $method = $_SERVER['REQUEST_METHOD'];
    $uri = $_SERVER['REQUEST_URI'];
    $ip = $_SERVER['REMOTE_ADDR'];
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    $user = getCurrentUser();
    $userId = $user ? $user['id'] : 'anonymous';
    
    logMessage('info', "Request: $method $uri", [
        'ip' => $ip,
        'user_id' => $userId,
        'user_agent' => $userAgent
    ]);
}

/**
 * Error Handler Middleware
 */
function errorHandlerMiddleware() {
    set_error_handler(function($severity, $message, $file, $line) {
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        logMessage('error', "PHP Error: $message", [
            'file' => $file,
            'line' => $line,
            'severity' => $severity
        ]);
        
        global $config;
        if ($config['app']['debug']) {
            echo "<b>Error:</b> $message in <b>$file</b> on line <b>$line</b><br>";
        }
        
        return true;
    });
    
    set_exception_handler(function($exception) {
        logMessage('error', 'Uncaught Exception: ' . $exception->getMessage(), [
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString()
        ]);
        
        global $config;
        if ($config['app']['debug']) {
            echo "<h1>Uncaught Exception</h1>";
            echo "<p><b>Message:</b> " . $exception->getMessage() . "</p>";
            echo "<p><b>File:</b> " . $exception->getFile() . "</p>";
            echo "<p><b>Line:</b> " . $exception->getLine() . "</p>";
            echo "<pre>" . $exception->getTraceAsString() . "</pre>";
        } else {
            http_response_code(500);
            echo "<h1>Internal Server Error</h1>";
            echo "<p>An error occurred. Please try again later.</p>";
        }
    });
}

/**
 * Apply all middleware
 */
function applyMiddleware() {
    errorHandlerMiddleware();
    securityHeadersMiddleware();
    contentTypeMiddleware();
    sessionCleanupMiddleware();
    loggingMiddleware();
    
    // Only apply rate limiting and CSRF to non-static requests
    $uri = $_SERVER['REQUEST_URI'];
    if (!preg_match('/\.(css|js|png|jpg|jpeg|gif|ico|svg)$/i', $uri)) {
        rateLimitMiddleware();
        
        // Only apply CSRF to POST requests
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            csrfMiddleware();
        }
    }
}

/**
 * JSON Response Helper
 */
function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data, JSON_PRETTY_PRINT);
    exit;
}

/**
 * Error Response Helper
 */
function errorResponse($message, $statusCode = 400, $details = null) {
    $response = ['error' => $message];
    if ($details) {
        $response['details'] = $details;
    }
    jsonResponse($response, $statusCode);
}

/**
 * Success Response Helper
 */
function successResponse($data = null, $message = 'Success') {
    $response = ['success' => true, 'message' => $message];
    if ($data) {
        $response['data'] = $data;
    }
    jsonResponse($response);
}

/**
 * Validate JSON Input
 */
function validateJsonInput($requiredFields = []) {
    $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
    
    if (strpos($contentType, 'application/json') !== false) {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            errorResponse('Invalid JSON input', 400);
        }
        
        foreach ($requiredFields as $field) {
            if (!isset($input[$field])) {
                errorResponse("Missing required field: $field", 400);
            }
        }
        
        return $input;
    }
    
    return $_POST;
}
