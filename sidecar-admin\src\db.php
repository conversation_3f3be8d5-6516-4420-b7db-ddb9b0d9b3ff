<?php
/**
 * Database Connection and Utilities
 */

/**
 * Create PDO database connection
 */
function createDatabaseConnection($config) {
    if (isset($config['connection']) && $config['connection'] === 'sqlite') {
        $dsn = 'sqlite:' . $config['database'];
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ];
        return new PDO($dsn, null, null, $options);
    }

    // MySQL connection (default)
    $dsn = sprintf(
        'mysql:host=%s;port=%d;dbname=%s;charset=%s',
        $config['host'],
        $config['port'],
        $config['name'],
        $config['charset']
    );

    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$config['charset']} COLLATE {$config['charset']}_unicode_ci"
    ];

    return new PDO($dsn, $config['user'], $config['pass'], $options);
}

/**
 * Execute a query with parameters and return results
 */
function executeQuery(PDO $pdo, $sql, $params = []) {
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt;
}

/**
 * Get a single row from database
 */
function fetchOne(PDO $pdo, $sql, $params = []) {
    $stmt = executeQuery($pdo, $sql, $params);
    return $stmt->fetch();
}

/**
 * Get all rows from database
 */
function fetchAll(PDO $pdo, $sql, $params = []) {
    $stmt = executeQuery($pdo, $sql, $params);
    return $stmt->fetchAll();
}

/**
 * Insert a record and return the last insert ID
 */
function insertRecord(PDO $pdo, $table, $data) {
    $columns = array_keys($data);
    $placeholders = array_map(function($col) { return ":$col"; }, $columns);
    
    $sql = sprintf(
        'INSERT INTO %s (%s) VALUES (%s)',
        $table,
        implode(', ', $columns),
        implode(', ', $placeholders)
    );
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($data);
    
    return $pdo->lastInsertId();
}

/**
 * Update records in database
 */
function updateRecord(PDO $pdo, $table, $data, $where, $whereParams = []) {
    $setParts = [];
    foreach (array_keys($data) as $column) {
        $setParts[] = "$column = :$column";
    }
    
    $sql = sprintf(
        'UPDATE %s SET %s WHERE %s',
        $table,
        implode(', ', $setParts),
        $where
    );
    
    $params = array_merge($data, $whereParams);
    $stmt = $pdo->prepare($sql);
    return $stmt->execute($params);
}

/**
 * Delete records from database
 */
function deleteRecord(PDO $pdo, $table, $where, $params = []) {
    $sql = "DELETE FROM $table WHERE $where";
    $stmt = $pdo->prepare($sql);
    return $stmt->execute($params);
}

/**
 * Begin database transaction
 */
function beginTransaction(PDO $pdo) {
    return $pdo->beginTransaction();
}

/**
 * Commit database transaction
 */
function commitTransaction(PDO $pdo) {
    return $pdo->commit();
}

/**
 * Rollback database transaction
 */
function rollbackTransaction(PDO $pdo) {
    return $pdo->rollBack();
}

/**
 * Check if record exists
 */
function recordExists(PDO $pdo, $table, $where, $params = []) {
    $sql = "SELECT 1 FROM $table WHERE $where LIMIT 1";
    $stmt = executeQuery($pdo, $sql, $params);
    return $stmt->fetchColumn() !== false;
}

/**
 * Get table configuration from global config
 */
function getTableName($tableName) {
    global $config;
    return $config['vendor']['tables'][$tableName] ?? $tableName;
}

/**
 * Get column configuration from global config
 */
function getColumnName($columnName) {
    global $config;
    return $config['vendor']['columns'][$columnName] ?? $columnName;
}

/**
 * Execute multiple queries in a transaction
 */
function executeInTransaction(PDO $pdo, callable $callback) {
    try {
        beginTransaction($pdo);
        $result = $callback($pdo);
        commitTransaction($pdo);
        return $result;
    } catch (Exception $e) {
        rollbackTransaction($pdo);
        throw $e;
    }
}

/**
 * Lock user records for update (used in transfers)
 */
function lockUsersForUpdate(PDO $pdo, array $userIds) {
    $placeholders = str_repeat('?,', count($userIds) - 1) . '?';
    $userTable = getTableName('users');
    $userIdCol = getColumnName('user_id');
    $balanceCol = getColumnName('user_balance');
    
    $sql = "SELECT $userIdCol as id, $balanceCol as balance 
            FROM $userTable 
            WHERE $userIdCol IN ($placeholders) 
            FOR UPDATE";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($userIds);
    
    $users = [];
    while ($row = $stmt->fetch()) {
        $users[$row['id']] = (float)$row['balance'];
    }
    
    return $users;
}

/**
 * Update user balance in vendor table
 */
function updateUserBalance(PDO $pdo, $userId, $newBalance) {
    $userTable = getTableName('users');
    $userIdCol = getColumnName('user_id');
    $balanceCol = getColumnName('user_balance');
    
    $sql = "UPDATE $userTable 
            SET $balanceCol = :balance, updated_at = NOW() 
            WHERE $userIdCol = :user_id";
    
    $stmt = $pdo->prepare($sql);
    return $stmt->execute([
        ':balance' => $newBalance,
        ':user_id' => $userId
    ]);
}
