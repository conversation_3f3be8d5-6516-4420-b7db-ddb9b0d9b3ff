<?php

use PHPUnit\Framework\TestCase;

class AgentTransfersTest extends TestCase {
    
    private $userId;
    private $agentId;
    private $cashUserId;
    private $walletUserId;
    
    protected function setUp(): void {
        global $config;
        
        // Clean up
        cleanupTestData();
        
        // Create test user
        $this->userId = createTestUser('<EMAIL>', 'Finance');
        
        // Create test agent
        $this->agentId = createTestAgent(9001, 5000.00);
        
        // Get system account IDs
        $this->cashUserId = $config['business']['nestblock_cash_user_id'];
        $this->walletUserId = $config['business']['nestblock_wallet_user_id'];
        
        // Login test user
        loginTestUser($this->userId);
    }
    
    protected function tearDown(): void {
        cleanupTestData();
    }
    
    public function testAgentToNestblockCashTransfer() {
        global $pdo;
        
        $send_amount = 1000.00;
        $initialAgentBalance = getVendorBalance($this->agentId);
        $initialCashBalance = getVendorBalance($this->cashUserId);
        
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'agent_user_id' => $this->agentId,
            'direction' => 'AgentToNestblockCash',
            'send_amount' => $send_amount,
            'send_curr' => 'USD',
            'reason' => 'Test transfer from agent to NestblockCash system'
        ];
        
        ob_start();
        try {
            $controller->transfer();
        } catch (Exception $e) {
            // Expected due to redirect
        }
        ob_end_clean();
        
        // Verify vendor balances updated correctly
        $finalAgentBalance = getVendorBalance($this->agentId);
        $finalCashBalance = getVendorBalance($this->cashUserId);
        
        assertBalanceEquals($initialAgentBalance - $send_amount, $finalAgentBalance, 'Agent balance should decrease');
        assertBalanceEquals($initialCashBalance + $send_amount, $finalCashBalance, 'Cash balance should increase');
        
        // Verify sidecar ledger entries
        $syncAudit = fetchOne($pdo, 
            'SELECT * FROM sidecar_vendor_sync_audits WHERE agent_user_id = ? ORDER BY created_at DESC LIMIT 1',
            [$this->agentId]
        );
        
        $this->assertNotNull($syncAudit);
        $this->assertEquals('NestblockCash', $syncAudit['counterparty_type']);
        $this->assertEquals(-$send_amount, $syncAudit['agent_delta']);
        $this->assertEquals($send_amount, $syncAudit['counterparty_delta']);
        
        // Verify exactly 2 ledger entries
        $ledgerCount = countLedgerEntries($syncAudit['tx_id']);
        $this->assertEquals(2, $ledgerCount);
        
        // Verify vendor transactions created
        $vendorTxCount = countVendorTransactions('SC-' . $syncAudit['tx_id'] . '-%');
        $this->assertEquals(2, $vendorTxCount);
    }
    
    public function testNestblockCashToAgentTransfer() {
        global $pdo;
        
        $send_amount = 500.00;
        $initialAgentBalance = getVendorBalance($this->agentId);
        $initialCashBalance = getVendorBalance($this->cashUserId);
        
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'agent_user_id' => $this->agentId,
            'direction' => 'NestblockCashToAgent',
            'send_amount' => $send_amount,
            'send_curr' => 'USD',
            'reason' => 'Test transfer from NestblockCash to agent'
        ];
        
        ob_start();
        try {
            $controller->transfer();
        } catch (Exception $e) {
            // Expected due to redirect
        }
        ob_end_clean();
        
        // Verify vendor balances
        $finalAgentBalance = getVendorBalance($this->agentId);
        $finalCashBalance = getVendorBalance($this->cashUserId);
        
        assertBalanceEquals($initialAgentBalance + $send_amount, $finalAgentBalance, 'Agent balance should increase');
        assertBalanceEquals($initialCashBalance - $send_amount, $finalCashBalance, 'Cash balance should decrease');
        
        // Verify sync audit
        $syncAudit = fetchOne($pdo, 
            'SELECT * FROM sidecar_vendor_sync_audits WHERE agent_user_id = ? ORDER BY created_at DESC LIMIT 1',
            [$this->agentId]
        );
        
        $this->assertEquals($send_amount, $syncAudit['agent_delta']);
        $this->assertEquals(-$send_amount, $syncAudit['counterparty_delta']);
    }
    
    public function testAgentToNestblockWalletTransfer() {
        global $pdo;
        
        $send_amount = 750.00;
        $initialAgentBalance = getVendorBalance($this->agentId);
        $initialWalletBalance = getVendorBalance($this->walletUserId);
        
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'agent_user_id' => $this->agentId,
            'direction' => 'AgentToNestblockWallet',
            'send_amount' => $send_amount,
            'send_curr' => 'USD',
            'reason' => 'Test transfer from agent to NestblockWallet system'
        ];
        
        ob_start();
        try {
            $controller->transfer();
        } catch (Exception $e) {
            // Expected due to redirect
        }
        ob_end_clean();
        
        // Verify vendor balances
        $finalAgentBalance = getVendorBalance($this->agentId);
        $finalWalletBalance = getVendorBalance($this->walletUserId);
        
        assertBalanceEquals($initialAgentBalance - $send_amount, $finalAgentBalance, 'Agent balance should decrease');
        assertBalanceEquals($initialWalletBalance + $send_amount, $finalWalletBalance, 'Wallet balance should increase');
        
        // Verify sync audit
        $syncAudit = fetchOne($pdo, 
            'SELECT * FROM sidecar_vendor_sync_audits WHERE agent_user_id = ? ORDER BY created_at DESC LIMIT 1',
            [$this->agentId]
        );
        
        $this->assertEquals('NestblockWallet', $syncAudit['counterparty_type']);
    }
    
    public function testNestblockWalletToAgentTransfer() {
        $send_amount = 300.00;
        $initialAgentBalance = getVendorBalance($this->agentId);
        $initialWalletBalance = getVendorBalance($this->walletUserId);
        
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'agent_user_id' => $this->agentId,
            'direction' => 'NestblockWalletToAgent',
            'send_amount' => $send_amount,
            'send_curr' => 'USD',
            'reason' => 'Test transfer from NestblockWallet to agent'
        ];
        
        ob_start();
        try {
            $controller->transfer();
        } catch (Exception $e) {
            // Expected due to redirect
        }
        ob_end_clean();
        
        // Verify vendor balances
        $finalAgentBalance = getVendorBalance($this->agentId);
        $finalWalletBalance = getVendorBalance($this->walletUserId);
        
        assertBalanceEquals($initialAgentBalance + $send_amount, $finalAgentBalance, 'Agent balance should increase');
        assertBalanceEquals($initialWalletBalance - $send_amount, $finalWalletBalance, 'Wallet balance should decrease');
    }
    
    public function testInsufficientFundsBlocked() {
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'agent_user_id' => $this->agentId,
            'direction' => 'AgentToNestblockCash',
            'send_amount' => 10000.00, // More than agent balance
            'send_curr' => 'USD',
            'reason' => 'Should fail due to insufficient funds'
        ];
        
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Insufficient agent balance');
        
        $controller->transfer();
    }
    
    public function testTransferValidation() {
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        
        // Test invalid send_amount
        $_POST = [
            'agent_user_id' => $this->agentId,
            'direction' => 'AgentToNestblockCash',
            'send_amount' => -100.00, // Negative send_amount
            'send_curr' => 'USD',
            'reason' => 'Valid reason'
        ];
        
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid send_amount');
        
        $controller->transfer();
    }
    
    public function testDoubleEntryBookkeeping() {
        global $pdo;
        
        $send_amount = 200.00;
        
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'agent_user_id' => $this->agentId,
            'direction' => 'AgentToNestblockCash',
            'send_amount' => $send_amount,
            'send_curr' => 'USD',
            'reason' => 'Test double-entry bookkeeping'
        ];
        
        ob_start();
        try {
            $controller->transfer();
        } catch (Exception $e) {
            // Expected due to redirect
        }
        ob_end_clean();
        
        // Get transaction ID
        $syncAudit = fetchOne($pdo, 
            'SELECT tx_id FROM sidecar_vendor_sync_audits WHERE agent_user_id = ? ORDER BY created_at DESC LIMIT 1',
            [$this->agentId]
        );
        
        $txId = $syncAudit['tx_id'];
        
        // Verify ledger entries balance
        $ledgerEntries = fetchAll($pdo, 
            'SELECT direction, send_amount FROM sidecar_ledger_entries WHERE tx_id = ?',
            [$txId]
        );
        
        $this->assertCount(2, $ledgerEntries);
        
        $totalDebits = 0;
        $totalCredits = 0;
        
        foreach ($ledgerEntries as $entry) {
            if ($entry['direction'] === 'Debit') {
                $totalDebits += $entry['send_amount'];
            } else {
                $totalCredits += $entry['send_amount'];
            }
        }
        
        assertBalanceEquals($totalDebits, $totalCredits, 'Debits must equal credits');
        assertBalanceEquals($send_amount, $totalDebits, 'Total debits should equal transfer send_amount');
    }
    
    public function testVendorTransactionRecords() {
        global $pdo;
        
        $send_amount = 150.00;
        
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'agent_user_id' => $this->agentId,
            'direction' => 'AgentToNestblockCash',
            'send_amount' => $send_amount,
            'send_curr' => 'USD',
            'reason' => 'Test vendor transaction records'
        ];
        
        ob_start();
        try {
            $controller->transfer();
        } catch (Exception $e) {
            // Expected due to redirect
        }
        ob_end_clean();
        
        // Get transaction ID
        $syncAudit = fetchOne($pdo, 
            'SELECT tx_id FROM sidecar_vendor_sync_audits WHERE agent_user_id = ? ORDER BY created_at DESC LIMIT 1',
            [$this->agentId]
        );
        
        $txId = $syncAudit['tx_id'];
        
        // Verify vendor transactions
        $vendorTransactions = fetchAll($pdo, 
            'SELECT user_id, send_amount, trx_type, trx_id FROM transactions WHERE trx_id LIKE ?',
            ['SC-' . $txId . '-%']
        );
        
        $this->assertCount(2, $vendorTransactions);
        
        // Find agent and cash transactions
        $agentTx = null;
        $cashTx = null;
        
        foreach ($vendorTransactions as $tx) {
            if ($tx['user_id'] == $this->agentId) {
                $agentTx = $tx;
            } elseif ($tx['user_id'] == $this->cashUserId) {
                $cashTx = $tx;
            }
        }
        
        $this->assertNotNull($agentTx, 'Agent transaction should exist');
        $this->assertNotNull($cashTx, 'Cash transaction should exist');
        
        // Verify transaction types and send_amounts
        $this->assertEquals('-', $agentTx['trx_type'], 'Agent transaction should be negative');
        $this->assertEquals('+', $cashTx['trx_type'], 'Cash transaction should be positive');
        $this->assertEquals($send_amount, $agentTx['send_amount'], 'Agent transaction send_amount');
        $this->assertEquals($send_amount, $cashTx['send_amount'], 'Cash transaction send_amount');
    }

    public function testSidecarLedgerBalanceCalculation() {
        global $pdo;

        // Perform multiple transfers to test balance calculation
        $transfers = [
            ['direction' => 'AgentToNestblockCash', 'send_amount' => 100.00],
            ['direction' => 'NestblockCashToAgent', 'send_amount' => 50.00],
            ['direction' => 'AgentToNestblockWallet', 'send_amount' => 75.00]
        ];

        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();

        foreach ($transfers as $transfer) {
            $_SERVER['REQUEST_METHOD'] = 'POST';
            $_POST = [
                'agent_user_id' => $this->agentId,
                'direction' => $transfer['direction'],
                'send_amount' => $transfer['send_amount'],
                'send_curr' => 'USD',
                'reason' => 'Test balance calculation'
            ];

            ob_start();
            try {
                $controller->transfer();
            } catch (Exception $e) {
                // Expected due to redirect
            }
            ob_end_clean();
        }

        // Calculate expected sidecar balance for agent
        // Agent: -100 + 50 - 75 = -125 (net outflow)
        $expectedAgentSidecarBalance = -125.00;
        $actualAgentSidecarBalance = getSidecarLedgerBalance($this->agentId, 'Agent');

        assertBalanceEquals($expectedAgentSidecarBalance, $actualAgentSidecarBalance,
            'Sidecar agent balance calculation');

        // Verify vendor balance matches
        $vendorBalance = getVendorBalance($this->agentId);
        $initialBalance = 5000.00; // From setUp
        $expectedVendorBalance = $initialBalance - 100.00 + 50.00 - 75.00;

        assertBalanceEquals($expectedVendorBalance, $vendorBalance, 'Vendor balance should match expected');
    }
}
