<?php
/**
 * Route Definitions and Handler
 */

/**
 * Simple router class
 */
class Router {
    private $routes = [];
    
    public function get($path, $handler) {
        $this->addRoute('GET', $path, $handler);
    }
    
    public function post($path, $handler) {
        $this->addRoute('POST', $path, $handler);
    }
    
    private function addRoute($method, $path, $handler) {
        $this->routes[] = [
            'method' => $method,
            'path' => $path,
            'handler' => $handler
        ];
    }
    
    public function dispatch($method, $uri) {
        // Remove query string
        $uri = strtok($uri, '?');
        
        foreach ($this->routes as $route) {
            if ($route['method'] === $method && $this->matchPath($route['path'], $uri)) {
                return $this->callHandler($route['handler'], $uri, $route['path']);
            }
        }
        
        // 404 Not Found
        http_response_code(404);
        include __DIR__ . '/views/404.php';
        return;
    }
    
    private function matchPath($routePath, $uri) {
        // Convert route path to regex
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $routePath);
        $pattern = '#^' . $pattern . '$#';
        
        return preg_match($pattern, $uri);
    }
    
    private function callHandler($handler, $uri, $routePath) {
        // Extract parameters
        $params = $this->extractParams($routePath, $uri);
        
        if (is_callable($handler)) {
            return call_user_func($handler, $params);
        }
        
        if (is_string($handler)) {
            list($controller, $method) = explode('@', $handler);
            $controllerFile = __DIR__ . "/controllers/{$controller}.php";
            
            if (file_exists($controllerFile)) {
                require_once $controllerFile;
                $controllerInstance = new $controller();
                return call_user_func([$controllerInstance, $method], $params);
            }
        }
        
        throw new Exception("Handler not found: $handler");
    }
    
    private function extractParams($routePath, $uri) {
        $routeParts = explode('/', trim($routePath, '/'));
        $uriParts = explode('/', trim($uri, '/'));
        $params = [];
        
        for ($i = 0; $i < count($routeParts); $i++) {
            if (preg_match('/\{([^}]+)\}/', $routeParts[$i], $matches)) {
                $params[$matches[1]] = $uriParts[$i] ?? null;
            }
        }
        
        return $params;
    }
}

// Initialize router
$router = new Router();

// Define routes
$router->get('/', function() {
    requireAuth();
    include __DIR__ . '/views/dashboard.php';
});

$router->get('/login', function() {
    if (isLoggedIn()) {
        redirect(baseUrl());
    }
    include __DIR__ . '/views/login.php';
});

$router->post('/login', 'AuthController@login');
$router->post('/logout', 'AuthController@logout');

// Beneficiary amendment routes
$router->get('/beneficiaries/amend', function() {
    requireRole(['Admin', 'Compliance']);
    // Allow access without remittance_id - show search interface
    include __DIR__ . '/views/beneficiary_amend_form.php';
});

$router->post('/beneficiaries/amend', 'BeneficiaryController@amend');

// Agent transfer routes
$router->get('/agents/transfer', function() {
    requireRole(['Admin', 'Finance']);
    include __DIR__ . '/views/agent_transfer_form.php';
});

$router->post('/agents/transfer', 'AccountingController@transfer');

$router->get('/agents/transfer/result', function() {
    requireRole(['Admin', 'Finance']);
    $txId = $_GET['tx_id'] ?? '';

    if (!$txId) {
        setFlashMessage('error', 'Transaction ID is required');
        redirect(baseUrl('agents/transfer'));
    }

    global $pdo;

    try {
        // Get transfer details
        $transfer = fetchOne($pdo, "
            SELECT vsa.*,
                   su.email as created_by_email,
                   au.name as agent_name,
                   au.email as agent_email
            FROM sidecar_vendor_sync_audits vsa
            JOIN sidecar_users su ON vsa.created_by_user_id = su.id
            LEFT JOIN users au ON vsa.agent_user_id = au.id
            WHERE vsa.tx_id = ?
        ", [$txId]);

        if (!$transfer) {
            setFlashMessage('error', 'Transfer record not found');
            redirect(baseUrl('agents/transfer'));
        }

        // Add created_by for display
        $transfer['created_by'] = $transfer['created_by_email'];
        $transfer['created_at'] = $transfer['created_at'];

        include __DIR__ . '/views/agent_transfer_result.php';

    } catch (Exception $e) {
        logMessage('error', 'Transfer result error: ' . $e->getMessage(), ['tx_id' => $txId]);
        setFlashMessage('error', 'Unable to load transfer details');
        redirect(baseUrl('agents/transfer'));
    }
});

// API routes for AJAX calls
$router->get('/api/agents/search', function() {
    requireRole(['Admin', 'Finance']);
    $query = $_GET['q'] ?? '';
    if (strlen($query) < 2) {
        jsonResponse([]);
    }
    
    global $pdo;
    $userTable = getTableName('users');
    $agents = fetchAll($pdo, 
        "SELECT id, username, email, balance FROM $userTable 
         WHERE (username LIKE ? OR email LIKE ?) 
         AND id NOT IN (?, ?) 
         ORDER BY username LIMIT 10",
        ["%$query%", "%$query%", 
         $GLOBALS['config']['business']['nestblock_cash_user_id'],
         $GLOBALS['config']['business']['nestblock_wallet_user_id']]
    );
    
    jsonResponse($agents);
});

$router->get('/api/agents/{id}/balance', function($params) {
    requireRole(['Admin', 'Finance']);
    $agentId = (int)$params['id'];
    
    global $pdo;
    $userTable = getTableName('users');
    $userIdCol = getColumnName('user_id');
    $balanceCol = getColumnName('user_balance');
    
    $agent = fetchOne($pdo, 
        "SELECT $userIdCol as id, username, $balanceCol as balance 
         FROM $userTable WHERE $userIdCol = ?", 
        [$agentId]
    );
    
    if (!$agent) {
        errorResponse('Agent not found', 404);
    }
    
    jsonResponse($agent);
});

// API route for remittance search
$router->get('/api/remittances/search', function() {
    requireRole(['Admin', 'Compliance']);
    $query = $_GET['q'] ?? '';

    if (strlen($query) < 2) {
        jsonResponse([]);
        return;
    }

    global $pdo;

    // Search in send_money table by invoice
    $results = fetchAll($pdo,
        "SELECT invoice, recipient_name, recipient_email, recipient_contact_no,
                status, send_amount, send_curr, created_at
         FROM send_money
         WHERE invoice LIKE ?
         ORDER BY created_at DESC
         LIMIT 20",
        ['%' . $query . '%']
    );

    jsonResponse($results);
});

$router->get('/api/remittances/{invoice}', function($params) {
    requireRole(['Admin', 'Compliance']);
    $invoiceNo = $params['invoice'];

    global $pdo;

    $remittance = fetchOne($pdo,
        "SELECT invoice, recipient_name, recipient_email, recipient_contact_no,
                status, send_amount, send_curr, created_at
         FROM send_money
         WHERE invoice = ?",
        [$invoiceNo]
    );

    if (!$remittance) {
        errorResponse('Remittance not found', 404);
    }

    jsonResponse($remittance);
});

// Reports and audit routes
$router->get('/reports/audits', 'ReportsController@audits');
$router->get('/reports/statements', 'ReportsController@statements');

// Export the router
$GLOBALS['router'] = $router;
