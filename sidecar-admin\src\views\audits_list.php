<?php
$title = 'Audit Trail - Sidecar Admin';
ob_start();
?>

<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?= baseUrl() ?>">Dashboard</a></li>
                <li class="breadcrumb-item active">Audit Trail</li>
            </ol>
        </nav>
        
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-file-text"></i> Audit Trail
            </h1>
            <div>
                <button type="button" class="btn btn-outline-secondary" data-bs-toggle="collapse" data-bs-target="#filters">
                    <i class="bi bi-funnel"></i> Filters
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="collapse mb-4" id="filters">
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="bi bi-funnel"></i> Filter Audit Records
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" action="<?= baseUrl('reports/audits') ?>">
                <div class="row">
                    <div class="col-md-3">
                        <label for="tx_id" class="form-label">Transaction ID</label>
                        <input type="text" class="form-control" id="tx_id" name="tx_id" 
                               value="<?= htmlspecialchars($_GET['tx_id'] ?? '') ?>"
                               placeholder="Enter TX ID">
                    </div>
                    <div class="col-md-3">
                        <label for="type" class="form-label">Type</label>
                        <select class="form-select" id="type" name="type">
                            <option value="">All Types</option>
                            <option value="amendment" <?= ($_GET['type'] ?? '') === 'amendment' ? 'selected' : '' ?>>
                                Beneficiary Amendment
                            </option>
                            <option value="transfer" <?= ($_GET['type'] ?? '') === 'transfer' ? 'selected' : '' ?>>
                                Agent Transfer
                            </option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="date_from" class="form-label">From Date</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" 
                               value="<?= htmlspecialchars($_GET['date_from'] ?? '') ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="date_to" class="form-label">To Date</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" 
                               value="<?= htmlspecialchars($_GET['date_to'] ?? '') ?>">
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search"></i> Filter
                        </button>
                        <a href="<?= baseUrl('reports/audits') ?>" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i> Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Results -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-list"></i> Audit Records
                    <?php if (isset($totalCount)): ?>
                        <span class="badge bg-secondary"><?= number_format($totalCount) ?></span>
                    <?php endif; ?>
                </h5>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="exportAudits()">
                        <i class="bi bi-download"></i> Export
                    </button>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($audits)): ?>
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-inbox fs-1"></i>
                        <p class="mt-3">No audit records found</p>
                        <?php if (!empty($_GET)): ?>
                            <a href="<?= baseUrl('reports/audits') ?>" class="btn btn-outline-primary">
                                <i class="bi bi-arrow-left"></i> View All Records
                            </a>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Date/Time</th>
                                    <th>Type</th>
                                    <th>Description</th>
                                    <th>User</th>
                                    <th>TX ID</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($audits as $audit): ?>
                                    <tr>
                                        <td>
                                            <small>
                                                <?= date('M j, Y', strtotime($audit['created_at'])) ?><br>
                                                <?= date('g:i:s A', strtotime($audit['created_at'])) ?>
                                            </small>
                                        </td>
                                        <td>
                                            <?php if ($audit['type'] === 'amendment'): ?>
                                                <span class="badge bg-primary">
                                                    <i class="bi bi-person-lines-fill"></i> Amendment
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-success">
                                                    <i class="bi bi-arrow-left-right"></i> Transfer
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="fw-bold"><?= htmlspecialchars($audit['description']) ?></div>
                                            <?php if ($audit['type'] === 'amendment'): ?>
                                                <small class="text-muted">
                                                    <?= htmlspecialchars($audit['details']['old_name']) ?> → 
                                                    <?= htmlspecialchars($audit['details']['new_name']) ?>
                                                </small>
                                            <?php else: ?>
                                                <small class="text-muted">
                                                    Agent #<?= $audit['details']['agent_user_id'] ?> ↔ 
                                                    <?= htmlspecialchars($audit['details']['counterparty_type']) ?>
                                                </small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small><?= htmlspecialchars($audit['user_email']) ?></small>
                                        </td>
                                        <td>
                                            <code class="small"><?= htmlspecialchars($audit['tx_id']) ?></code>
                                        </td>
                                        <td>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-info" 
                                                    onclick="showAuditDetails('<?= htmlspecialchars($audit['tx_id']) ?>', '<?= $audit['type'] ?>')"
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#auditModal">
                                                <i class="bi bi-eye"></i> Details
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if (isset($totalPages) && $totalPages > 1): ?>
                        <nav aria-label="Audit pagination">
                            <ul class="pagination justify-content-center">
                                <?php
                                $currentPage = $page ?? 1;
                                $queryParams = $_GET;
                                ?>
                                
                                <?php if ($currentPage > 1): ?>
                                    <?php
                                    $queryParams['page'] = $currentPage - 1;
                                    $prevUrl = baseUrl('reports/audits?' . http_build_query($queryParams));
                                    ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?= $prevUrl ?>">Previous</a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $currentPage - 2); $i <= min($totalPages, $currentPage + 2); $i++): ?>
                                    <?php
                                    $queryParams['page'] = $i;
                                    $pageUrl = baseUrl('reports/audits?' . http_build_query($queryParams));
                                    ?>
                                    <li class="page-item <?= $i === $currentPage ? 'active' : '' ?>">
                                        <a class="page-link" href="<?= $pageUrl ?>"><?= $i ?></a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($currentPage < $totalPages): ?>
                                    <?php
                                    $queryParams['page'] = $currentPage + 1;
                                    $nextUrl = baseUrl('reports/audits?' . http_build_query($queryParams));
                                    ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?= $nextUrl ?>">Next</a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Audit Details Modal -->
<div class="modal fade" id="auditModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Audit Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="auditModalBody">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<?php
$additionalJs = '
<script>
function showAuditDetails(txId, type) {
    const modalBody = document.getElementById("auditModalBody");
    modalBody.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    `;
    
    // Find the audit record in the current page data
    const audits = ' . json_encode($audits) . ';
    const audit = audits.find(a => a.tx_id === txId);
    
    if (audit) {
        displayAuditDetails(audit);
    } else {
        modalBody.innerHTML = `<div class="alert alert-danger">Unable to load audit details</div>`;
    }
}

function displayAuditDetails(audit) {
    const modalBody = document.getElementById("auditModalBody");
    
    let detailsHtml = `
        <div class="row mb-3">
            <div class="col-md-6">
                <strong>Transaction ID:</strong><br>
                <code>${audit.tx_id}</code>
            </div>
            <div class="col-md-6">
                <strong>Date/Time:</strong><br>
                ${new Date(audit.created_at).toLocaleString()}
            </div>
        </div>
        <div class="row mb-3">
            <div class="col-md-6">
                <strong>Type:</strong><br>
                ${audit.type === "amendment" ? "Beneficiary Amendment" : "Agent Transfer"}
            </div>
            <div class="col-md-6">
                <strong>User:</strong><br>
                ${audit.user_email}
            </div>
        </div>
        <div class="mb-3">
            <strong>Description:</strong><br>
            ${audit.description}
        </div>
    `;
    
    if (audit.type === "amendment") {
        detailsHtml += `
            <div class="mb-3">
                <strong>Changes:</strong>
                <table class="table table-sm table-bordered">
                    <tr>
                        <th>Field</th>
                        <th>Old Value</th>
                        <th>New Value</th>
                    </tr>
                    <tr>
                        <td>Name</td>
                        <td>${audit.details.old_name || "N/A"}</td>
                        <td>${audit.details.new_name || "N/A"}</td>
                    </tr>
                    <tr>
                        <td>Email</td>
                        <td>${audit.details.old_email || "N/A"}</td>
                        <td>${audit.details.new_email || "N/A"}</td>
                    </tr>
                    <tr>
                        <td>Phone</td>
                        <td>${audit.details.old_phone || "N/A"}</td>
                        <td>${audit.details.new_phone || "N/A"}</td>
                    </tr>
                </table>
            </div>
            <div class="mb-3">
                <strong>Reason:</strong><br>
                <div class="bg-light p-2 rounded">${audit.details.reason}</div>
            </div>
        `;
    } else {
        detailsHtml += `
            <div class="mb-3">
                <strong>Transfer Details:</strong>
                <table class="table table-sm table-bordered">
                    <tr>
                        <th>Agent ID</th>
                        <td>#${audit.details.agent_user_id}</td>
                    </tr>
                    <tr>
                        <th>Counterparty</th>
                        <td>${audit.details.counterparty_type} (#${audit.details.counterparty_user_id})</td>
                    </tr>
                    <tr>
                        <th>Currency</th>
                        <td>${audit.details.send_curr}</td>
                    </tr>
                    <tr>
                        <th>Agent Balance</th>
                        <td>${formatCurrency(audit.details.agent_before)} → ${formatCurrency(audit.details.agent_after)}</td>
                    </tr>
                    <tr>
                        <th>Counterparty Balance</th>
                        <td>${formatCurrency(audit.details.counterparty_before)} → ${formatCurrency(audit.details.counterparty_after)}</td>
                    </tr>
                </table>
            </div>
            <div class="mb-3">
                <strong>Reason:</strong><br>
                <div class="bg-light p-2 rounded">${audit.details.reason}</div>
            </div>
        `;
    }
    
    modalBody.innerHTML = detailsHtml;
}

function exportAudits() {
    const params = new URLSearchParams(window.location.search);
    params.set("export", "csv");
    window.location.href = "' . baseUrl('reports/audits') . '?" + params.toString();
}

function formatCurrency(send_amount, send_curr = "USD") {
    return new Intl.NumberFormat("en-US", {
        style: "send_curr",
        send_curr: send_curr
    }).format(send_amount);
}
</script>
';

$content = ob_get_clean();
include __DIR__ . '/layout.php';
?>
