<?php

require_once __DIR__ . '/../bootstrap.php';

class AgentDepositWithdrawTest extends PHPUnit\Framework\TestCase {
    
    private $agentId;
    private $cashUserId;
    private $walletUserId;
    
    protected function setUp(): void {
        global $pdo, $config;
        
        // Clean up any existing test data
        executeQuery($pdo, 'DELETE FROM sidecar_vendor_sync_audits WHERE agent_user_id IN (SELECT id FROM users WHERE email LIKE "%test%")');
        executeQuery($pdo, 'DELETE FROM sidecar_ledger_entries WHERE tx_id IN (SELECT tx_id FROM sidecar_vendor_sync_audits WHERE agent_user_id IN (SELECT id FROM users WHERE email LIKE "%test%"))');
        
        // Create test agent
        $this->agentId = createTestUser('<EMAIL>', 'Agent Test User', 1000.00);
        
        // Get system account IDs
        $this->cashUserId = $config['business']['nestblock_cash_user_id'];
        $this->walletUserId = $config['business']['nestblock_wallet_user_id'];
        
        // Ensure system accounts exist with initial balances
        ensureTestUser($this->cashUserId, '<EMAIL>', 'NestBlock Cash', 5000.00);
        ensureTestUser($this->walletUserId, '<EMAIL>', 'NestBlock Wallet', 3000.00);
    }
    
    protected function tearDown(): void {
        global $pdo;
        
        // Clean up test data
        executeQuery($pdo, 'DELETE FROM sidecar_vendor_sync_audits WHERE agent_user_id = ?', [$this->agentId]);
        executeQuery($pdo, 'DELETE FROM users WHERE id = ?', [$this->agentId]);
    }
    
    public function testAgentDepositCash() {
        global $pdo;
        
        $depositAmount = 500.00;
        $initialAgentBalance = getVendorBalance($this->agentId);
        $initialCashBalance = getVendorBalance($this->cashUserId);
        
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'agent_user_id' => $this->agentId,
            'operation' => 'AgentDepositCash',
            'send_amount' => $depositAmount,
            'send_curr' => 'USD',
            'reason' => 'Test cash deposit by agent'
        ];
        
        ob_start();
        try {
            $controller->depositWithdraw();
        } catch (Exception $e) {
            // Expected due to redirect
        }
        ob_end_clean();
        
        // Verify vendor balances updated correctly (both should increase)
        $finalAgentBalance = getVendorBalance($this->agentId);
        $finalCashBalance = getVendorBalance($this->cashUserId);
        
        assertBalanceEquals($initialAgentBalance + $depositAmount, $finalAgentBalance, 'Agent balance should increase');
        assertBalanceEquals($initialCashBalance + $depositAmount, $finalCashBalance, 'Cash balance should increase');
        
        // Verify sidecar ledger entries
        $syncAudit = fetchOne($pdo, 
            'SELECT * FROM sidecar_vendor_sync_audits WHERE agent_user_id = ? ORDER BY created_at DESC LIMIT 1',
            [$this->agentId]
        );
        
        $this->assertNotNull($syncAudit);
        $this->assertEquals('NestblockCash', $syncAudit['counterparty_type']);
        $this->assertEquals($depositAmount, $syncAudit['agent_delta']);
        $this->assertEquals($depositAmount, $syncAudit['counterparty_delta']);
        
        // Verify exactly 2 ledger entries
        $ledgerCount = countLedgerEntries($syncAudit['tx_id']);
        $this->assertEquals(2, $ledgerCount);
        
        // Verify vendor transactions created
        $vendorTxCount = countVendorTransactions('SC-' . $syncAudit['tx_id'] . '-%');
        $this->assertEquals(2, $vendorTxCount);
    }
    
    public function testAgentWithdrawCash() {
        global $pdo;
        
        $withdrawAmount = 300.00;
        $initialAgentBalance = getVendorBalance($this->agentId);
        $initialCashBalance = getVendorBalance($this->cashUserId);
        
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'agent_user_id' => $this->agentId,
            'operation' => 'AgentWithdrawCash',
            'send_amount' => $withdrawAmount,
            'send_curr' => 'USD',
            'reason' => 'Test cash withdrawal by agent'
        ];
        
        ob_start();
        try {
            $controller->depositWithdraw();
        } catch (Exception $e) {
            // Expected due to redirect
        }
        ob_end_clean();
        
        // Verify vendor balances updated correctly (both should decrease)
        $finalAgentBalance = getVendorBalance($this->agentId);
        $finalCashBalance = getVendorBalance($this->cashUserId);
        
        assertBalanceEquals($initialAgentBalance - $withdrawAmount, $finalAgentBalance, 'Agent balance should decrease');
        assertBalanceEquals($initialCashBalance - $withdrawAmount, $finalCashBalance, 'Cash balance should decrease');
        
        // Verify sync audit
        $syncAudit = fetchOne($pdo, 
            'SELECT * FROM sidecar_vendor_sync_audits WHERE agent_user_id = ? ORDER BY created_at DESC LIMIT 1',
            [$this->agentId]
        );
        
        $this->assertEquals(-$withdrawAmount, $syncAudit['agent_delta']);
        $this->assertEquals(-$withdrawAmount, $syncAudit['counterparty_delta']);
    }
    
    public function testAgentDepositWallet() {
        global $pdo;
        
        $depositAmount = 750.00;
        $initialAgentBalance = getVendorBalance($this->agentId);
        $initialWalletBalance = getVendorBalance($this->walletUserId);
        
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'agent_user_id' => $this->agentId,
            'operation' => 'AgentDepositWallet',
            'send_amount' => $depositAmount,
            'send_curr' => 'USD',
            'reason' => 'Test wallet deposit by agent'
        ];
        
        ob_start();
        try {
            $controller->depositWithdraw();
        } catch (Exception $e) {
            // Expected due to redirect
        }
        ob_end_clean();
        
        // Verify vendor balances (both should increase)
        $finalAgentBalance = getVendorBalance($this->agentId);
        $finalWalletBalance = getVendorBalance($this->walletUserId);
        
        assertBalanceEquals($initialAgentBalance + $depositAmount, $finalAgentBalance, 'Agent balance should increase');
        assertBalanceEquals($initialWalletBalance + $depositAmount, $finalWalletBalance, 'Wallet balance should increase');
        
        // Verify sync audit
        $syncAudit = fetchOne($pdo, 
            'SELECT * FROM sidecar_vendor_sync_audits WHERE agent_user_id = ? ORDER BY created_at DESC LIMIT 1',
            [$this->agentId]
        );
        
        $this->assertEquals($depositAmount, $syncAudit['agent_delta']);
        $this->assertEquals($depositAmount, $syncAudit['counterparty_delta']);
    }
    
    public function testAgentWithdrawWallet() {
        global $pdo;
        
        $withdrawAmount = 200.00;
        $initialAgentBalance = getVendorBalance($this->agentId);
        $initialWalletBalance = getVendorBalance($this->walletUserId);
        
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'agent_user_id' => $this->agentId,
            'operation' => 'AgentWithdrawWallet',
            'send_amount' => $withdrawAmount,
            'send_curr' => 'USD',
            'reason' => 'Test wallet withdrawal by agent'
        ];
        
        ob_start();
        try {
            $controller->depositWithdraw();
        } catch (Exception $e) {
            // Expected due to redirect
        }
        ob_end_clean();
        
        // Verify vendor balances (both should decrease)
        $finalAgentBalance = getVendorBalance($this->agentId);
        $finalWalletBalance = getVendorBalance($this->walletUserId);
        
        assertBalanceEquals($initialAgentBalance - $withdrawAmount, $finalAgentBalance, 'Agent balance should decrease');
        assertBalanceEquals($initialWalletBalance - $withdrawAmount, $finalWalletBalance, 'Wallet balance should decrease');
        
        // Verify sync audit
        $syncAudit = fetchOne($pdo, 
            'SELECT * FROM sidecar_vendor_sync_audits WHERE agent_user_id = ? ORDER BY created_at DESC LIMIT 1',
            [$this->agentId]
        );
        
        $this->assertEquals(-$withdrawAmount, $syncAudit['agent_delta']);
        $this->assertEquals(-$withdrawAmount, $syncAudit['counterparty_delta']);
    }
    
    public function testInsufficientFundsWithdrawal() {
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'agent_user_id' => $this->agentId,
            'operation' => 'AgentWithdrawCash',
            'send_amount' => 10000.00, // More than agent has
            'send_curr' => 'USD',
            'reason' => 'Test insufficient funds withdrawal'
        ];
        
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Insufficient agent balance for withdrawal');
        
        $controller->depositWithdraw();
    }
    
    public function testOperationValidation() {
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        
        // Test invalid operation
        $_POST = [
            'agent_user_id' => $this->agentId,
            'operation' => 'InvalidOperation',
            'send_amount' => 100.00,
            'send_curr' => 'USD',
            'reason' => 'Valid reason'
        ];
        
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid operation type');
        
        $controller->depositWithdraw();
    }
    
    public function testDoubleEntryBookkeeping() {
        global $pdo;
        
        $depositAmount = 200.00;
        
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'agent_user_id' => $this->agentId,
            'operation' => 'AgentDepositCash',
            'send_amount' => $depositAmount,
            'send_curr' => 'USD',
            'reason' => 'Test double-entry bookkeeping'
        ];
        
        ob_start();
        try {
            $controller->depositWithdraw();
        } catch (Exception $e) {
            // Expected due to redirect
        }
        ob_end_clean();
        
        // Get transaction ID
        $syncAudit = fetchOne($pdo, 
            'SELECT tx_id FROM sidecar_vendor_sync_audits WHERE agent_user_id = ? ORDER BY created_at DESC LIMIT 1',
            [$this->agentId]
        );
        
        $txId = $syncAudit['tx_id'];
        
        // Verify ledger entries balance
        $ledgerEntries = fetchAll($pdo, 
            'SELECT direction, send_amount FROM sidecar_ledger_entries WHERE tx_id = ?',
            [$txId]
        );
        
        $this->assertCount(2, $ledgerEntries);
        
        $totalDebits = 0;
        $totalCredits = 0;
        
        foreach ($ledgerEntries as $entry) {
            if ($entry['direction'] === 'Debit') {
                $totalDebits += $entry['send_amount'];
            } else {
                $totalCredits += $entry['send_amount'];
            }
        }
        
        assertBalanceEquals($totalDebits, $totalCredits, 'Debits must equal credits');
        assertBalanceEquals($depositAmount, $totalDebits, 'Total debits should equal deposit amount');
    }
}
