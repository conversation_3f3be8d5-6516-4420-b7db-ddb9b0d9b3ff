<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.5/phpunit.xsd"
         bootstrap="bootstrap.php"
         cacheResultFile=".phpunit.result.cache"
         executionOrder="depends,defects"
         forceCoversAnnotation="false"
         beStrictAboutCoversAnnotation="true"
         beStrictAboutOutputDuringTests="true"
         beStrictAboutTodoAnnotatedTests="true"
         convertDeprecationsToExceptions="true"
         failOnRisky="true"
         failOnWarning="true"
         verbose="true">
    <testsuites>
        <testsuite name="Feature">
            <directory suffix="Test.php">./Feature</directory>
        </testsuite>
    </testsuites>

    <coverage cacheDirectory=".phpunit.cache/code-coverage"
              processUncoveredFiles="true">
        <include>
            <directory suffix=".php">../src</directory>
        </include>
        <exclude>
            <directory>../src/views</directory>
        </exclude>
    </coverage>

    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="DB_HOST" value="127.0.0.1"/>
        <env name="DB_PORT" value="3306"/>
        <env name="DB_NAME" value="sidecar_test"/>
        <env name="DB_USER" value="sidecar_ops"/>
        <env name="DB_PASS" value="change_me"/>
        <env name="SESSION_SECRET" value="test_secret_key_32_characters"/>
        <env name="NESTBLOCK_CASH_USER_ID" value="1001"/>
        <env name="NESTBLOCK_WALLET_USER_ID" value="1002"/>
    </php>
</phpunit>
