<?php
/**
 * Beneficiary Controller
 * Handles beneficiary amendment operations for send_money table
 */

class BeneficiaryController {

    /**
     * Handle beneficiary amendment request
     */
    public function amend($params = []) {
        requireRole(['Admin', 'Compliance']);

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(baseUrl('beneficiaries/amend'));
        }

        global $pdo, $config;

        // Get and validate input
        $invoiceNo = sanitizeInput($_POST['invoice_no'] ?? '');
        $newName = sanitizeInput($_POST['name'] ?? '');
        $newEmail = sanitizeInput($_POST['email'] ?? '');
        $newPhone = sanitizeInput($_POST['phone'] ?? '');
        $reason = sanitizeInput($_POST['reason'] ?? '');

        try {
            // Validate input
            if (empty($invoiceNo)) {
                throw new InvalidArgumentException('Invoice number is required');
            }

            if (empty($newName) || strlen($newName) > 255) {
                throw new InvalidArgumentException('Name is required and must be less than 255 characters');
            }

            if (empty($newEmail) || strlen($newEmail) > 255 || !filter_var($newEmail, FILTER_VALIDATE_EMAIL)) {
                throw new InvalidArgumentException('Valid email is required and must be less than 255 characters');
            }

            if (empty($newPhone) || strlen($newPhone) > 50) {
                throw new InvalidArgumentException('Phone is required and must be less than 50 characters');
            }

            if (empty($reason) || strlen($reason) < 5) {
                throw new InvalidArgumentException('Reason is required and must be at least 5 characters');
            }

            // Execute amendment in transaction
            $result = executeInTransaction($pdo, function($pdo) use ($invoiceNo, $newName, $newEmail, $newPhone, $reason, $config) {
                return $this->performAmendment($pdo, $invoiceNo, $newName, $newEmail, $newPhone, $reason, $config);
            });

            // Success response
            setFlashMessage('success', 'Beneficiary details amended successfully. Transaction ID: ' . $result['tx_id']);

            // Redirect back to amendment page with invoice number pre-populated
            redirect(baseUrl('beneficiaries/amend?invoice_no=' . urlencode($invoiceNo)));

        } catch (Exception $e) {
            logMessage('error', 'Beneficiary amendment error: ' . $e->getMessage(), [
                'invoice' => $invoiceNo,
                'user_id' => getCurrentUser()['id'],
                'reason' => $reason
            ]);

            setFlashMessage('error', $e->getMessage());
            redirect(baseUrl('beneficiaries/amend?invoice=' . urlencode($invoiceNo)));
        }
    }
    
    /**
     * Perform the actual amendment operation
     */
    private function performAmendment($pdo, $invoiceNo, $newName, $newEmail, $newPhone, $reason, $config) {
        $user = getCurrentUser();

        // Lock and fetch remittance data from send_money table
        $sql = "SELECT invoice, recipient_name, recipient_email, recipient_contact_no,
                       status, send_amount, send_curr
                FROM send_money
                WHERE invoice = ?
                FOR UPDATE";

        $remittance = fetchOne($pdo, $sql, [$invoiceNo]);

        if (!$remittance) {
            throw new InvalidArgumentException('Remittance not found');
        }

        // Check if remittance status allows amendment
        $disallowedStatuses = $config['business']['remittance_disallowed_statuses'] ??
                             ['PaidOut', 'Cancelled', 'Refunded', 'Failed'];

        if (in_array($remittance['status'], $disallowedStatuses)) {
            throw new InvalidArgumentException(
                "Cannot amend beneficiary for remittance with status: {$remittance['status']}"
            );
        }

        // Check if there's actually a change
        $nameChanged = $remittance['recipient_name'] !== $newName;
        $emailChanged = $remittance['recipient_email'] !== $newEmail;
        $phoneChanged = $remittance['recipient_contact_no'] !== $newPhone;

        if (!$nameChanged && !$emailChanged && !$phoneChanged) {
            throw new InvalidArgumentException('No changes detected in beneficiary details');
        }

        // Generate transaction ID
        $txId = generateUuid();

        // Check for idempotency (in case of retry)
        $existingAudit = fetchOne($pdo,
            'SELECT tx_id FROM sidecar_beneficiary_amend_audits WHERE tx_id = ?',
            [$txId]
        );

        if ($existingAudit) {
            throw new InvalidArgumentException('Transaction already processed');
        }

        // Insert audit record
        insertRecord($pdo, 'sidecar_beneficiary_amend_audits', [
            'tx_id' => $txId,
            'invoice' => $invoiceNo,
            'changed_by_user_id' => $user['id'],
            'old_name' => $remittance['recipient_name'],
            'new_name' => $newName,
            'old_email' => $remittance['recipient_email'],
            'new_email' => $newEmail,
            'old_phone' => $remittance['recipient_contact_no'],
            'new_phone' => $newPhone,
            'reason' => $reason
        ]);

        // Update beneficiary details in send_money table
        $updateSql = "UPDATE send_money
                      SET recipient_name = ?,
                          recipient_email = ?,
                          recipient_contact_no = ?,
                          updated_at = NOW()
                      WHERE invoice = ?";

        $stmt = $pdo->prepare($updateSql);
        $success = $stmt->execute([$newName, $newEmail, $newPhone, $invoiceNo]);

        if (!$success) {
            throw new RuntimeException('Failed to update beneficiary details');
        }

        // Log the successful amendment
        logMessage('info', 'Beneficiary amended successfully', [
            'tx_id' => $txId,
            'invoice' => $invoiceNo,
            'user_id' => $user['id'],
            'changes' => [
                'name' => ['from' => $remittance['recipient_name'], 'to' => $newName],
                'email' => ['from' => $remittance['recipient_email'], 'to' => $newEmail],
                'phone' => ['from' => $remittance['recipient_contact_no'], 'to' => $newPhone]
            ]
        ]);

        return [
            'tx_id' => $txId,
            'invoice' => $invoiceNo,
            'changes' => [
                'name' => [
                    'old' => $remittance['recipient_name'],
                    'new' => $newName
                ],
                'email' => [
                    'old' => $remittance['recipient_email'],
                    'new' => $newEmail
                ],
                'phone' => [
                    'old' => $remittance['recipient_contact_no'],
                    'new' => $newPhone
                ]
            ],
            'reason' => $reason,
            'amended_by' => $user['email'],
            'amended_at' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Show amendment result
     */
    public function amendResult($params = []) {
        requireRole(['Admin', 'Compliance']);

        $txId = $_GET['tx_id'] ?? '';

        if (!$txId) {
            setFlashMessage('error', 'Transaction ID is required');
            redirect(baseUrl('beneficiaries/amend'));
        }

        global $pdo;

        try {
            // Get amendment details
            $amendment = fetchOne($pdo, "
                SELECT ba.*,
                       su.email as amended_by_email,
                       sm.send_amount as remittance_send_amount,
                       sm.send_curr as remittance_send_curr,
                       sm.status as remittance_status
                FROM sidecar_beneficiary_amend_audits ba
                JOIN sidecar_users su ON ba.changed_by_user_id = su.id
                LEFT JOIN send_money sm ON ba.invoice = sm.invoice
                WHERE ba.tx_id = ?
            ", [$txId]);

            if (!$amendment) {
                setFlashMessage('error', 'Amendment record not found');
                redirect(baseUrl('beneficiaries/amend'));
            }

            include __DIR__ . '/../views/beneficiary_amend_result.php';

        } catch (Exception $e) {
            logMessage('error', 'Amendment result error: ' . $e->getMessage(), ['tx_id' => $txId]);
            setFlashMessage('error', 'Unable to load amendment details');
            redirect(baseUrl('beneficiaries/amend'));
        }
    }
}
