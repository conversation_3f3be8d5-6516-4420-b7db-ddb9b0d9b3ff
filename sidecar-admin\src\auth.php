<?php
/**
 * Authentication Functions
 */

/**
 * Authenticate user with email and password
 */
function authenticateUser($email, $password) {
    global $pdo;
    
    $user = fetchOne($pdo, 
        'SELECT id, email, password_hash, role FROM sidecar_users WHERE email = ?', 
        [$email]
    );
    
    if ($user && password_verify($password, $user['password_hash'])) {
        // Remove password hash from user data
        unset($user['password_hash']);
        return $user;
    }
    
    return false;
}

/**
 * Login user and create session
 */
function loginUser($user) {
    global $pdo, $config;
    
    // Regenerate session ID for security
    session_regenerate_id(true);
    
    // Store user in session
    $_SESSION['user'] = $user;
    $_SESSION['login_time'] = time();
    
    // Create session record in database
    $sessionId = session_id();
    $expiresAt = date('Y-m-d H:i:s', time() + $config['session']['lifetime']);
    
    // Clean up old sessions for this user
    executeQuery($pdo, 
        'DELETE FROM sidecar_sessions WHERE user_id = ?', 
        [$user['id']]
    );
    
    // Insert new session
    insertRecord($pdo, 'sidecar_sessions', [
        'id' => $sessionId,
        'user_id' => $user['id'],
        'expires_at' => $expiresAt
    ]);
    
    logMessage('info', 'User logged in', ['user_id' => $user['id'], 'email' => $user['email']]);
}

/**
 * Logout user and destroy session
 */
function logoutUser() {
    global $pdo;
    
    $user = getCurrentUser();
    if ($user) {
        // Remove session from database
        executeQuery($pdo, 
            'DELETE FROM sidecar_sessions WHERE id = ?', 
            [session_id()]
        );
        
        logMessage('info', 'User logged out', ['user_id' => $user['id'], 'email' => $user['email']]);
    }
    
    // Destroy session
    $_SESSION = [];
    session_destroy();
}

/**
 * Check if current session is valid
 */
function isValidSession() {
    global $pdo;
    
    if (!isLoggedIn()) {
        return false;
    }
    
    $sessionId = session_id();
    $session = fetchOne($pdo, 
        'SELECT expires_at FROM sidecar_sessions WHERE id = ?', 
        [$sessionId]
    );
    
    if (!$session) {
        return false;
    }
    
    // Check if session has expired
    if (strtotime($session['expires_at']) < time()) {
        // Clean up expired session
        executeQuery($pdo, 
            'DELETE FROM sidecar_sessions WHERE id = ?', 
            [$sessionId]
        );
        return false;
    }
    
    return true;
}

/**
 * Require authentication
 */
function requireAuth() {
    if (!isValidSession()) {
        redirect(baseUrl('login'));
    }
}

/**
 * Require specific role(s)
 */
function requireRole($requiredRoles) {
    requireAuth();
    
    if (!hasRole($requiredRoles)) {
        http_response_code(403);
        setFlashMessage('error', 'Access denied. Insufficient permissions.');
        redirect(baseUrl());
    }
}

/**
 * Hash password
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Create new user
 */
function createUser($email, $password, $role = 'Viewer') {
    global $pdo;
    
    if (!isValidEmail($email)) {
        throw new InvalidArgumentException('Invalid email address');
    }
    
    $validRoles = ['Admin', 'Compliance', 'Finance', 'Viewer'];
    if (!in_array($role, $validRoles)) {
        throw new InvalidArgumentException('Invalid role');
    }
    
    // Check if user already exists
    if (recordExists($pdo, 'sidecar_users', 'email = ?', [$email])) {
        throw new InvalidArgumentException('User with this email already exists');
    }
    
    $userId = insertRecord($pdo, 'sidecar_users', [
        'email' => $email,
        'password_hash' => hashPassword($password),
        'role' => $role
    ]);
    
    logMessage('info', 'New user created', ['user_id' => $userId, 'email' => $email, 'role' => $role]);
    
    return $userId;
}

/**
 * Update user password
 */
function updateUserPassword($userId, $newPassword) {
    global $pdo;
    
    updateRecord($pdo, 'sidecar_users', 
        ['password_hash' => hashPassword($newPassword)], 
        'id = ?', 
        [$userId]
    );
    
    logMessage('info', 'User password updated', ['user_id' => $userId]);
}

/**
 * Clean up expired sessions
 */
function cleanupExpiredSessions() {
    global $pdo;
    
    $count = executeQuery($pdo, 
        'DELETE FROM sidecar_sessions WHERE expires_at < NOW()'
    )->rowCount();
    
    if ($count > 0) {
        logMessage('info', 'Cleaned up expired sessions', ['count' => $count]);
    }
    
    return $count;
}

/**
 * Get user by ID
 */
function getUserById($userId) {
    global $pdo;
    
    return fetchOne($pdo, 
        'SELECT id, email, role, created_at FROM sidecar_users WHERE id = ?', 
        [$userId]
    );
}

/**
 * Get all users
 */
function getAllUsers() {
    global $pdo;
    
    return fetchAll($pdo, 
        'SELECT id, email, role, created_at FROM sidecar_users ORDER BY email'
    );
}

/**
 * Update user role
 */
function updateUserRole($userId, $newRole) {
    global $pdo;
    
    $validRoles = ['Admin', 'Compliance', 'Finance', 'Viewer'];
    if (!in_array($newRole, $validRoles)) {
        throw new InvalidArgumentException('Invalid role');
    }
    
    updateRecord($pdo, 'sidecar_users', 
        ['role' => $newRole], 
        'id = ?', 
        [$userId]
    );
    
    logMessage('info', 'User role updated', ['user_id' => $userId, 'new_role' => $newRole]);
}
