-- Seed data for sidecar admin users
-- Default password for all users is 'password123' (change in production)

INSERT INTO sidecar_users (email, password_hash, role) VALUES
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin'),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Finance'),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Compliance'),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Viewer');

-- Create some sample vendor data for testing (this would normally exist in vendor system)
-- Users table (vendor)
CREATE TABLE IF NOT EXISTS users (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  email VARCHAR(255) NOT NULL UNIQUE,
  name VARCHAR(255) NOT NULL,
  balance DECIMAL(18,2) NOT NULL DEFAULT 0.00,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Transactions table (vendor)
CREATE TABLE IF NOT EXISTS transactions (
  id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
  user_id BIGINT(20) NOT NULL,
  send_amount DOUBLE(10,2) DEFAULT NULL,
  charge DECIMAL(11,2) NOT NULL DEFAULT 0.00,
  final_balance VARCHAR(30) DEFAULT NULL,
  trx_type VARCHAR(10) DEFAULT NULL,
  remarks VARCHAR(255) NOT NULL,
  trx_id VARCHAR(255) DEFAULT NULL,
  created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_trx_id (trx_id),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Remittances table (vendor)
CREATE TABLE IF NOT EXISTS remittances (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  sender_id BIGINT UNSIGNED NOT NULL,
  beneficiary_id BIGINT UNSIGNED NOT NULL,
  send_amount DECIMAL(18,2) NOT NULL,
  send_curr CHAR(3) NOT NULL DEFAULT 'USD',
  status ENUM('Pending','Processing','PaidOut','Cancelled','Failed') NOT NULL DEFAULT 'Pending',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_status (status),
  INDEX idx_beneficiary (beneficiary_id)
);

-- Beneficiaries table (vendor)
CREATE TABLE IF NOT EXISTS beneficiaries (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(255) NOT NULL,
  phone VARCHAR(50) NOT NULL,
  email VARCHAR(255),
  address TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Sample vendor data
INSERT INTO users (id, email, name, balance) VALUES
(1001, '<EMAIL>', 'NestblockCash System', 1000000.00),
(1002, '<EMAIL>', 'NestblockWallet System', 500000.00),
(2001, '<EMAIL>', 'Agent One', 10000.00),
(2002, '<EMAIL>', 'Agent Two', 15000.00),
(2003, '<EMAIL>', 'Agent Three', 8000.00);

INSERT INTO beneficiaries (id, name, phone, email) VALUES
(1, 'John Doe', '+1234567890', '<EMAIL>'),
(2, 'Jane Smith', '+1234567891', '<EMAIL>'),
(3, 'Bob Johnson', '+1234567892', '<EMAIL>');

INSERT INTO remittances (id, sender_id, beneficiary_id, send_amount, status) VALUES
(1, 2001, 1, 500.00, 'Pending'),
(2, 2002, 2, 750.00, 'Processing'),
(3, 2003, 3, 300.00, 'PaidOut');
