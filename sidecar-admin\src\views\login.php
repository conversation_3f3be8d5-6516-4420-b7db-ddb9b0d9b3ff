<?php
$title = 'Login - Sidecar Admin';
ob_start();
?>

<div class="row justify-content-center" style="margin-top: 5rem;">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header text-center">
                <h4 class="mb-0">
                    <i class="bi bi-shield-check text-primary"></i>
                    Sidecar Admin
                </h4>
                <p class="text-muted mb-0">Please sign in to continue</p>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= baseUrl('login') ?>">
                    <input type="hidden" name="_token" value="<?= generateCsrfToken() ?>">
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address <span class="required">*</span></label>
                        <input type="email" 
                               class="form-control" 
                               id="email" 
                               name="email" 
                               required 
                               autofocus
                               value="<?= htmlspecialchars($_POST['email'] ?? '') ?>">
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">Password <span class="required">*</span></label>
                        <input type="password" 
                               class="form-control" 
                               id="password" 
                               name="password" 
                               required>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-box-arrow-in-right"></i> Sign In
                        </button>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center text-muted">
                <small>
                    <i class="bi bi-info-circle"></i>
                    Default credentials: <EMAIL> / password123
                </small>
            </div>
        </div>
    </div>
</div>

<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }
    
    .card {
        border: none;
        border-radius: 1rem;
    }
    
    .card-header {
        background: transparent;
        border-bottom: 1px solid rgba(0,0,0,.125);
        border-radius: 1rem 1rem 0 0 !important;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
    }
    
    .btn-primary:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-1px);
    }
</style>

<?php
$content = ob_get_clean();
include __DIR__ . '/layout.php';
?>
