###
# Sidecar Admin E2E API Tests
# Use with REST Client extension in VS Code or similar HTTP client
###

@baseUrl = http://localhost:8080
@sessionCookie = PHPSESSID=your_session_id_here

### 1. Login
POST {{baseUrl}}/login
Content-Type: application/x-www-form-urlencoded

email=<EMAIL>&password=admin123

### 2. Dashboard (requires authentication)
GET {{baseUrl}}/
Cookie: {{sessionCookie}}

### 3. Search for agents
GET {{baseUrl}}/api/agents/search?q=agent
<PERSON><PERSON>: {{sessionCookie}}

### 4. Get agent balance
GET {{baseUrl}}/api/agents/2001/balance
Cookie: {{sessionCookie}}

### 5. Beneficiary Amendment Form
GET {{baseUrl}}/beneficiary/amend?remittance_id=1001
Cookie: {{sessionCookie}}

### 6. Submit Beneficiary Amendment
POST {{baseUrl}}/beneficiary/amend
Cookie: {{sessionCookie}}
Content-Type: application/x-www-form-urlencoded

remittance_id=1001&name=Updated+Beneficiary+Name&phone=%2B1234567890&reason=Customer+requested+name+correction+due+to+passport+update

### 7. Agent Transfer Form
GET {{baseUrl}}/agent/transfer
Cookie: {{sessionCookie}}

### 8. Submit Agent Transfer (Agent to NestblockCash)
POST {{baseUrl}}/agent/transfer
Cookie: {{sessionCookie}}
Content-Type: application/x-www-form-urlencoded

agent_user_id=2001&direction=AgentToNestblockCash&send_amount=500.00&send_curr=USD&reason=Agent+deposit+to+NestblockCash+system

### 9. Submit Agent Transfer (NestblockCash to Agent)
POST {{baseUrl}}/agent/transfer
Cookie: {{sessionCookie}}
Content-Type: application/x-www-form-urlencoded

agent_user_id=2001&direction=NestblockCashToAgent&send_amount=250.00&send_curr=USD&reason=Refund+from+NestblockCash+to+agent

### 10. Submit Agent Transfer (Agent to NestblockWallet)
POST {{baseUrl}}/agent/transfer
Cookie: {{sessionCookie}}
Content-Type: application/x-www-form-urlencoded

agent_user_id=2001&direction=AgentToNestblockWallet&send_amount=300.00&send_curr=USD&reason=Agent+deposit+to+NestblockWallet+system

### 11. Submit Agent Transfer (NestblockWallet to Agent)
POST {{baseUrl}}/agent/transfer
Cookie: {{sessionCookie}}
Content-Type: application/x-www-form-urlencoded

agent_user_id=2001&direction=NestblockWalletToAgent&send_amount=150.00&send_curr=USD&reason=Withdrawal+from+NestblockWallet+to+agent

### 12. View Audit Trail
GET {{baseUrl}}/reports/audits
Cookie: {{sessionCookie}}

### 13. View Audit Trail with Filters
GET {{baseUrl}}/reports/audits?type=transfer&date_from=2024-01-01&date_to=2024-12-31
Cookie: {{sessionCookie}}

### 14. View Statements and Reconciliation
GET {{baseUrl}}/reports/statements
Cookie: {{sessionCookie}}

### 15. Logout
POST {{baseUrl}}/logout
Cookie: {{sessionCookie}}

###
# Error Cases
###

### 16. Attempt beneficiary amendment on paid out remittance (should fail)
POST {{baseUrl}}/beneficiary/amend
Cookie: {{sessionCookie}}
Content-Type: application/x-www-form-urlencoded

remittance_id=1002&name=Should+Fail&phone=%2B1234567890&reason=This+should+fail+if+remittance+is+paid+out

### 17. Attempt transfer with insufficient funds (should fail)
POST {{baseUrl}}/agent/transfer
Cookie: {{sessionCookie}}
Content-Type: application/x-www-form-urlencoded

agent_user_id=2001&direction=AgentToNestblockCash&send_amount=999999.00&send_curr=USD&reason=Should+fail+due+to+insufficient+funds

### 18. Attempt transfer with invalid send_amount (should fail)
POST {{baseUrl}}/agent/transfer
Cookie: {{sessionCookie}}
Content-Type: application/x-www-form-urlencoded

agent_user_id=2001&direction=AgentToNestblockCash&send_amount=-100.00&send_curr=USD&reason=Should+fail+due+to+negative+send_amount

### 19. Attempt access without authentication (should redirect to login)
GET {{baseUrl}}/agent/transfer

### 20. Attempt access with insufficient role (create viewer user first)
# First login as viewer
POST {{baseUrl}}/login
Content-Type: application/x-www-form-urlencoded

email=<EMAIL>&password=viewer123

# Then try to access admin function (should fail)
GET {{baseUrl}}/agent/transfer
Cookie: PHPSESSID=viewer_session_id_here

###
# CURL Examples (for command line testing)
###

# Login and get session cookie
# curl -c cookies.txt -X POST -d "email=<EMAIL>&password=admin123" http://localhost:8080/login

# Use session cookie for authenticated requests
# curl -b cookies.txt http://localhost:8080/

# Search agents
# curl -b cookies.txt "http://localhost:8080/api/agents/search?q=agent"

# Get agent balance
# curl -b cookies.txt http://localhost:8080/api/agents/2001/balance

# Submit beneficiary amendment
# curl -b cookies.txt -X POST -d "remittance_id=1001&name=Updated+Name&phone=%2B1234567890&reason=Customer+requested+correction" http://localhost:8080/beneficiary/amend

# Submit agent transfer
# curl -b cookies.txt -X POST -d "agent_user_id=2001&direction=AgentToNestblockCash&send_amount=500.00&send_curr=USD&reason=Test+transfer" http://localhost:8080/agent/transfer

# View audit trail
# curl -b cookies.txt http://localhost:8080/reports/audits

# View statements
# curl -b cookies.txt http://localhost:8080/reports/statements

# Logout
# curl -b cookies.txt -X POST http://localhost:8080/logout

###
# Database Verification Queries
# Run these in MySQL to verify operations
###

# Check beneficiary amendments
# SELECT * FROM sidecar_beneficiary_amend_audits ORDER BY created_at DESC LIMIT 10;

# Check agent transfers
# SELECT * FROM sidecar_vendor_sync_audits ORDER BY created_at DESC LIMIT 10;

# Check ledger entries
# SELECT * FROM sidecar_ledger_entries ORDER BY created_at DESC LIMIT 20;

# Check vendor transactions
# SELECT * FROM transactions WHERE trx_id LIKE 'SC-%' ORDER BY created_at DESC LIMIT 20;

# Verify balance consistency
# SELECT 
#   u.id, 
#   u.email, 
#   u.balance as vendor_balance,
#   COALESCE(sidecar_balance.balance, 0) as sidecar_balance
# FROM users u
# LEFT JOIN (
#   SELECT 
#     a.owner_user_id,
#     SUM(CASE WHEN le.direction = 'Debit' THEN le.send_amount ELSE -le.send_amount END) as balance
#   FROM sidecar_accounts a
#   JOIN sidecar_ledger_entries le ON a.id = le.account_id
#   WHERE a.account_type = 'Agent'
#   GROUP BY a.owner_user_id
# ) sidecar_balance ON u.id = sidecar_balance.owner_user_id
# WHERE u.id IN (SELECT DISTINCT agent_user_id FROM sidecar_vendor_sync_audits);

###
# Performance Testing
###

### Concurrent beneficiary amendments (run multiple times simultaneously)
POST {{baseUrl}}/beneficiary/amend
Cookie: {{sessionCookie}}
Content-Type: application/x-www-form-urlencoded

remittance_id=1001&name=Concurrent+Test+{{$randomInt}}&phone=%2B{{$randomInt}}&reason=Concurrent+amendment+test+{{$timestamp}}

### Concurrent agent transfers (run multiple times simultaneously)
POST {{baseUrl}}/agent/transfer
Cookie: {{sessionCookie}}
Content-Type: application/x-www-form-urlencoded

agent_user_id=2001&direction=AgentToNestblockCash&send_amount=10.00&send_curr=USD&reason=Concurrent+transfer+test+{{$timestamp}}

###
# Reconciliation Testing
###

### Run reconciliation via command line
# php bin/reconcile.php

### Run reconciliation with CSV output
# php bin/reconcile.php --csv

### Run reconciliation with HTML output
# php bin/reconcile.php --html

###
# Security Testing
###

### CSRF Protection Test (should fail without proper token)
POST {{baseUrl}}/beneficiary/amend
Cookie: {{sessionCookie}}
Content-Type: application/x-www-form-urlencoded

remittance_id=1001&name=CSRF+Test&phone=%2B1234567890&reason=Should+fail+without+CSRF+token

### SQL Injection Test (should be safely handled)
POST {{baseUrl}}/beneficiary/amend
Cookie: {{sessionCookie}}
Content-Type: application/x-www-form-urlencoded

remittance_id=1001'; DROP TABLE users; --&name=SQL+Injection+Test&phone=%2B1234567890&reason=SQL+injection+attempt

### XSS Test (should be safely escaped)
POST {{baseUrl}}/beneficiary/amend
Cookie: {{sessionCookie}}
Content-Type: application/x-www-form-urlencoded

remittance_id=1001&name=<script>alert('XSS')</script>&phone=%2B1234567890&reason=XSS+test+attempt
