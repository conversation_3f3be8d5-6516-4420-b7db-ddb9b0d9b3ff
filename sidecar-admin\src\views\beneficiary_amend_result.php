<?php
$title = 'Amendment Result - Sidecar Admin';
ob_start();
?>

<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?= baseUrl() ?>">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="<?= baseUrl('beneficiaries/amend') ?>">Amend Beneficiary</a></li>
                <li class="breadcrumb-item active">Result</li>
            </ol>
        </nav>
        
        <h1 class="mb-4">
            <i class="bi bi-check-circle text-success"></i> Amendment Completed
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="bi bi-check-circle"></i> Amendment Successful
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <h6><i class="bi bi-info-circle"></i> Transaction Details</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Transaction ID:</strong><br>
                            <code><?= htmlspecialchars($amendment['tx_id']) ?></code>
                        </div>
                        <div class="col-md-6">
                            <strong>Processed At:</strong><br>
                            <?= date('M j, Y g:i:s A', strtotime($amendment['created_at'])) ?>
                        </div>
                    </div>
                </div>
                
                <h6 class="mb-3">Remittance Information</h6>
                <div class="row mb-4">
                    <div class="col-md-4">
                        <strong>Invoice Number:</strong><br>
                        <?= htmlspecialchars($amendment['invoice']) ?>
                    </div>
                    <div class="col-md-4">
                        <strong>Status:</strong><br>
                        <?php
                        $status = $amendment['remittance_status'] ?? 'Unknown';
                        switch($status) {
                            case 'Pending': $statusColor = 'warning'; break;
                            case 'Processing': $statusColor = 'info'; break;
                            case 'PaidOut': $statusColor = 'success'; break;
                            case 'Cancelled': $statusColor = 'danger'; break;
                            case 'Failed': $statusColor = 'danger'; break;
                            case 'Refunded': $statusColor = 'secondary'; break;
                            default: $statusColor = 'secondary'; break;
                        }
                        ?>
                        <span class="badge bg-<?= $statusColor ?>">
                            <?= htmlspecialchars($status) ?>
                        </span>
                    </div>
                    <div class="col-md-4">
                        <strong>send_amount:</strong><br>
                        <?= formatCurrency($amendment['remittance_send_amount'] ?? 0, $amendment['remittance_send_curr'] ?? 'USD') ?>
                    </div>
                </div>
                
                <h6 class="mb-3">Changes Made</h6>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>Field</th>
                                <th>Previous Value</th>
                                <th>New Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if ($amendment['old_name'] !== $amendment['new_name']): ?>
                            <tr>
                                <td><strong>Name</strong></td>
                                <td>
                                    <span class="text-muted"><?= htmlspecialchars($amendment['old_name']) ?></span>
                                </td>
                                <td>
                                    <span class="text-success fw-bold"><?= htmlspecialchars($amendment['new_name']) ?></span>
                                </td>
                            </tr>
                            <?php endif; ?>
                            
                            <?php if (($amendment['old_email'] ?? '') !== ($amendment['new_email'] ?? '')): ?>
                            <tr>
                                <td><strong>Email</strong></td>
                                <td>
                                    <span class="text-muted"><?= htmlspecialchars($amendment['old_email'] ?? '') ?></span>
                                </td>
                                <td>
                                    <span class="text-success fw-bold"><?= htmlspecialchars($amendment['new_email'] ?? '') ?></span>
                                </td>
                            </tr>
                            <?php endif; ?>

                            <?php if ($amendment['old_phone'] !== $amendment['new_phone']): ?>
                            <tr>
                                <td><strong>Phone</strong></td>
                                <td>
                                    <span class="text-muted"><?= htmlspecialchars($amendment['old_phone']) ?></span>
                                </td>
                                <td>
                                    <span class="text-success fw-bold"><?= htmlspecialchars($amendment['new_phone']) ?></span>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                
                <h6 class="mb-3">Amendment Details</h6>
                <div class="audit-entry">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Amended By:</strong><br>
                            <?= htmlspecialchars($amendment['amended_by_email']) ?>
                        </div>
                        <div class="col-md-6">
                            <strong>Timestamp:</strong><br>
                            <?= date('M j, Y g:i:s A', strtotime($amendment['created_at'])) ?>
                        </div>
                    </div>
                    <div class="mt-3">
                        <strong>Reason:</strong><br>
                        <div class="bg-light p-3 rounded">
                            <?= nl2br(htmlspecialchars($amendment['reason'])) ?>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4 d-flex justify-content-between">
                    <a href="<?= baseUrl('beneficiaries/amend') ?>" class="btn btn-outline-primary">
                        <i class="bi bi-plus-circle"></i> Amend Another
                    </a>
                    <div>
                        <button type="button" class="btn btn-outline-secondary me-2" onclick="window.print()">
                            <i class="bi bi-printer"></i> Print
                        </button>
                        <a href="<?= baseUrl('reports/audits?tx_id=' . urlencode($amendment['tx_id'])) ?>" class="btn btn-info">
                            <i class="bi bi-file-text"></i> View in Audit Log
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-info-circle"></i> What Happens Next?
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-3">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Beneficiary Updated</strong><br>
                        <small class="text-muted">The beneficiary details have been updated in the system</small>
                    </li>
                    <li class="mb-3">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>Audit Trail Created</strong><br>
                        <small class="text-muted">This change has been logged for compliance purposes</small>
                    </li>
                    <li class="mb-3">
                        <i class="bi bi-info-circle text-info"></i>
                        <strong>Remittance Continues</strong><br>
                        <small class="text-muted">The remittance will proceed with the updated beneficiary details</small>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-shield-check"></i> Compliance Note
                </h6>
            </div>
            <div class="card-body">
                <p class="small text-muted mb-0">
                    This amendment has been recorded with transaction ID 
                    <code><?= htmlspecialchars($amendment['tx_id']) ?></code> 
                    and is permanently stored in the audit trail for regulatory compliance.
                </p>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-question-circle"></i> Need Help?
                </h6>
            </div>
            <div class="card-body">
                <p class="small mb-2">If you need to:</p>
                <ul class="small">
                    <li>Make additional changes to this beneficiary</li>
                    <li>Report an issue with this amendment</li>
                    <li>Access historical amendment records</li>
                </ul>
                <p class="small mb-0">
                    Please contact your system administrator with the transaction ID above.
                </p>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .card-header, nav, .col-lg-4 {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .audit-entry {
        border-left: 3px solid #000 !important;
    }
}
</style>

<?php
$content = ob_get_clean();
include __DIR__ . '/layout.php';
?>
