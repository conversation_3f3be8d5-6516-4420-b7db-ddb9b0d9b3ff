<?php
/**
 * Test the updated beneficiary amendment system
 */

require_once 'src/bootstrap.php';

echo "Testing updated beneficiary amendment system...\n";

try {
    // Test database connection
    echo "Database connection: ";
    $result = $pdo->query('SELECT 1 as test');
    echo "OK\n";
    
    // Test search functionality
    echo "Testing search API...\n";
    $searchResults = $pdo->query('SELECT invoice, recipient_name, recipient_email, recipient_contact_no, status, send_amount, send_curr FROM send_money WHERE invoice LIKE "%INV%" LIMIT 3')->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Found " . count($searchResults) . " remittances:\n";
    foreach ($searchResults as $result) {
        echo "- {$result['invoice']}: {$result['recipient_name']} ({$result['status']})\n";
    }
    
    // Test single lookup
    echo "\nTesting single lookup...\n";
    $single = $pdo->query('SELECT * FROM send_money WHERE invoice = "INV-001"')->fetch(PDO::FETCH_ASSOC);
    if ($single) {
        echo "Found: {$single['invoice']} - {$single['recipient_name']} ({$single['recipient_email']})\n";
    }
    
    // Test audit table structure
    echo "\nTesting audit table structure...\n";
    $auditColumns = $pdo->query('PRAGMA table_info(sidecar_beneficiary_amend_audits)')->fetchAll(PDO::FETCH_ASSOC);
    echo "Audit table columns:\n";
    foreach ($auditColumns as $col) {
        echo "- {$col['name']} ({$col['type']})\n";
    }
    
    echo "\nSystem is ready for testing!\n";
    echo "You can now access the beneficiary amendment page and test with:\n";
    echo "- INV-001 (Pending - can be amended)\n";
    echo "- INV-002 (Processing - can be amended)\n";
    echo "- INV-003 (PaidOut - cannot be amended)\n";
    
    echo "\nKey changes made:\n";
    echo "1. ✅ Fixed navigation access - no longer requires remittance_id parameter\n";
    echo "2. ✅ Updated to work with send_money table instead of separate remittances/beneficiaries\n";
    echo "3. ✅ Added email field support for recipient amendments\n";
    echo "4. ✅ Updated audit trail to track email changes\n";
    echo "5. ✅ Added search functionality by invoice number\n";
    echo "6. ✅ Updated all views to display invoice numbers instead of remittance IDs\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
