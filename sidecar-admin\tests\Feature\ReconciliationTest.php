<?php

use PHPUnit\Framework\TestCase;

class ReconciliationTest extends TestCase {
    
    private $userId;
    private $agentId;
    private $cashUserId;
    private $walletUserId;
    
    protected function setUp(): void {
        global $config;
        
        // Clean up
        cleanupTestData();
        
        // Create test user
        $this->userId = createTestUser('<EMAIL>', 'Finance');
        
        // Create test agent
        $this->agentId = createTestAgent(9003, 3000.00);
        
        // Get system account IDs
        $this->cashUserId = $config['business']['nestblock_cash_user_id'];
        $this->walletUserId = $config['business']['nestblock_wallet_user_id'];
        
        // Login test user
        loginTestUser($this->userId);
    }
    
    protected function tearDown(): void {
        cleanupTestData();
    }
    
    public function testReconciliationWithMatchingBalances() {
        global $pdo, $config;
        
        // Perform some transfers to create sidecar activity
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $transfers = [
            ['direction' => 'AgentToNestblockCash', 'send_amount' => 500.00],
            ['direction' => 'NestblockCashToAgent', 'send_amount' => 200.00],
            ['direction' => 'AgentToNestblockWallet', 'send_amount' => 100.00]
        ];
        
        foreach ($transfers as $transfer) {
            $_SERVER['REQUEST_METHOD'] = 'POST';
            $_POST = [
                'agent_user_id' => $this->agentId,
                'direction' => $transfer['direction'],
                'send_amount' => $transfer['send_amount'],
                'send_curr' => 'USD',
                'reason' => 'Test reconciliation'
            ];
            
            ob_start();
            try {
                $controller->transfer();
            } catch (Exception $e) {
                // Expected due to redirect
            }
            ob_end_clean();
        }
        
        // Run reconciliation
        require_once __DIR__ . '/../../src/controllers/ReportsController.php';
        $reportsController = new ReportsController();
        $reconciliation = $reportsController->performReconciliation($pdo, $config);
        
        // Verify reconciliation results
        $this->assertArrayHasKey('summary', $reconciliation);
        $this->assertArrayHasKey('ledger_balance', $reconciliation);
        $this->assertArrayHasKey('agent_balances', $reconciliation);
        $this->assertArrayHasKey('system_balances', $reconciliation);
        $this->assertArrayHasKey('discrepancies', $reconciliation);
        
        // Should have no discrepancies
        $this->assertEmpty($reconciliation['discrepancies'], 'Should have no discrepancies');
        $this->assertTrue($reconciliation['ledger_balance'], 'Ledger should be balanced');
        $this->assertEquals(0, $reconciliation['summary']['failed_checks'], 'Should have no failed checks');
    }
    
    public function testLedgerBalanceCheck() {
        global $pdo, $config;
        
        // Create a valid transfer first
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'agent_user_id' => $this->agentId,
            'direction' => 'AgentToNestblockCash',
            'send_amount' => 300.00,
            'send_curr' => 'USD',
            'reason' => 'Test ledger balance'
        ];
        
        ob_start();
        try {
            $controller->transfer();
        } catch (Exception $e) {
            // Expected due to redirect
        }
        ob_end_clean();
        
        // Get the transaction ID
        $syncAudit = fetchOne($pdo, 
            'SELECT tx_id FROM sidecar_vendor_sync_audits WHERE agent_user_id = ? ORDER BY created_at DESC LIMIT 1',
            [$this->agentId]
        );
        
        $txId = $syncAudit['tx_id'];
        
        // Verify ledger entries are balanced
        $ledgerEntries = fetchAll($pdo, 
            'SELECT direction, send_amount FROM sidecar_ledger_entries WHERE tx_id = ?',
            [$txId]
        );
        
        $this->assertCount(2, $ledgerEntries, 'Should have exactly 2 ledger entries');
        
        $totalDebits = 0;
        $totalCredits = 0;
        
        foreach ($ledgerEntries as $entry) {
            if ($entry['direction'] === 'Debit') {
                $totalDebits += $entry['send_amount'];
            } else {
                $totalCredits += $entry['send_amount'];
            }
        }
        
        assertBalanceEquals($totalDebits, $totalCredits, 'Debits should equal credits');
        
        // Run reconciliation to verify it detects balanced ledger
        require_once __DIR__ . '/../../src/controllers/ReportsController.php';
        $reportsController = new ReportsController();
        $reconciliation = $reportsController->performReconciliation($pdo, $config);
        
        $this->assertTrue($reconciliation['ledger_balance'], 'Reconciliation should detect balanced ledger');
    }
    
    public function testAgentBalanceReconciliation() {
        global $pdo, $config;
        
        $initialVendorBalance = getVendorBalance($this->agentId);
        
        // Perform transfer
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'agent_user_id' => $this->agentId,
            'direction' => 'AgentToNestblockCash',
            'send_amount' => 400.00,
            'send_curr' => 'USD',
            'reason' => 'Test agent balance reconciliation'
        ];
        
        ob_start();
        try {
            $controller->transfer();
        } catch (Exception $e) {
            // Expected due to redirect
        }
        ob_end_clean();
        
        // Run reconciliation
        require_once __DIR__ . '/../../src/controllers/ReportsController.php';
        $reportsController = new ReportsController();
        $reconciliation = $reportsController->performReconciliation($pdo, $config);
        
        // Verify agent balance reconciliation
        $this->assertArrayHasKey($this->agentId, $reconciliation['agent_balances']);
        
        $agentBalance = $reconciliation['agent_balances'][$this->agentId];
        $this->assertEquals('match', $agentBalance['status'], 'Agent balance should match');
        
        // Verify the actual balances
        $expectedVendorBalance = $initialVendorBalance - 400.00;
        assertBalanceEquals($expectedVendorBalance, $agentBalance['vendor'], 'Vendor balance should match expected');
        
        // Sidecar balance should reflect the net change
        $expectedSidecarBalance = -400.00; // Net outflow from agent
        assertBalanceEquals($expectedSidecarBalance, $agentBalance['sidecar'], 'Sidecar balance should match expected');
    }
    
    public function testSystemAccountBalanceReconciliation() {
        global $pdo, $config;
        
        $initialCashBalance = getVendorBalance($this->cashUserId);
        
        // Perform transfer that affects system account
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'agent_user_id' => $this->agentId,
            'direction' => 'AgentToNestblockCash',
            'send_amount' => 250.00,
            'send_curr' => 'USD',
            'reason' => 'Test system account reconciliation'
        ];
        
        ob_start();
        try {
            $controller->transfer();
        } catch (Exception $e) {
            // Expected due to redirect
        }
        ob_end_clean();
        
        // Run reconciliation
        require_once __DIR__ . '/../../src/controllers/ReportsController.php';
        $reportsController = new ReportsController();
        $reconciliation = $reportsController->performReconciliation($pdo, $config);
        
        // Verify system account balance reconciliation
        $this->assertArrayHasKey('NestblockCash', $reconciliation['system_balances']);
        
        $cashBalance = $reconciliation['system_balances']['NestblockCash'];
        $this->assertEquals('match', $cashBalance['status'], 'Cash balance should match');
        
        // Verify the actual balances
        $expectedVendorBalance = $initialCashBalance + 250.00;
        assertBalanceEquals($expectedVendorBalance, $cashBalance['vendor'], 'Cash vendor balance should match expected');
        
        // Sidecar balance should reflect the net change
        $expectedSidecarBalance = 250.00; // Net inflow to cash
        assertBalanceEquals($expectedSidecarBalance, $cashBalance['sidecar'], 'Cash sidecar balance should match expected');
    }
    
    public function testReconciliationDetectsImbalancedLedger() {
        global $pdo, $config;
        
        // Create a valid transfer first
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'agent_user_id' => $this->agentId,
            'direction' => 'AgentToNestblockCash',
            'send_amount' => 100.00,
            'send_curr' => 'USD',
            'reason' => 'Test imbalanced ledger detection'
        ];
        
        ob_start();
        try {
            $controller->transfer();
        } catch (Exception $e) {
            // Expected due to redirect
        }
        ob_end_clean();
        
        // Get the transaction ID
        $syncAudit = fetchOne($pdo, 
            'SELECT tx_id FROM sidecar_vendor_sync_audits WHERE agent_user_id = ? ORDER BY created_at DESC LIMIT 1',
            [$this->agentId]
        );
        
        $txId = $syncAudit['tx_id'];
        
        // Manually corrupt the ledger by adding an extra entry
        $agentAccount = fetchOne($pdo, 
            'SELECT id FROM sidecar_accounts WHERE owner_user_id = ? AND account_type = "Agent"',
            [$this->agentId]
        );
        
        insertRecord($pdo, 'sidecar_ledger_entries', [
            'tx_id' => $txId,
            'account_id' => $agentAccount['id'],
            'direction' => 'Debit',
            'send_amount' => 50.00,
            'description' => 'Corrupted entry for testing'
        ]);
        
        // Run reconciliation
        require_once __DIR__ . '/../../src/controllers/ReportsController.php';
        $reportsController = new ReportsController();
        $reconciliation = $reportsController->performReconciliation($pdo, $config);
        
        // Should detect the imbalance
        $this->assertFalse($reconciliation['ledger_balance'], 'Should detect imbalanced ledger');
        $this->assertNotEmpty($reconciliation['discrepancies'], 'Should have discrepancies');
        $this->assertGreaterThan(0, $reconciliation['summary']['failed_checks'], 'Should have failed checks');
        
        // Find the specific discrepancy
        $ledgerDiscrepancy = null;
        foreach ($reconciliation['discrepancies'] as $discrepancy) {
            if ($discrepancy['type'] === 'ledger_imbalance' && $discrepancy['tx_id'] === $txId) {
                $ledgerDiscrepancy = $discrepancy;
                break;
            }
        }
        
        $this->assertNotNull($ledgerDiscrepancy, 'Should find ledger imbalance discrepancy');
        $this->assertStringContainsString('Ledger imbalance', $ledgerDiscrepancy['description']);
    }
    
    public function testReconciliationWithMultipleAgents() {
        global $pdo, $config;
        
        // Create additional test agents
        $agent2Id = createTestAgent(9004, 1500.00);
        $agent3Id = createTestAgent(9005, 2500.00);
        
        // Perform transfers for multiple agents
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $transfers = [
            ['agent' => $this->agentId, 'direction' => 'AgentToNestblockCash', 'send_amount' => 300.00],
            ['agent' => $agent2Id, 'direction' => 'NestblockCashToAgent', 'send_amount' => 150.00],
            ['agent' => $agent3Id, 'direction' => 'AgentToNestblockWallet', 'send_amount' => 200.00],
            ['agent' => $this->agentId, 'direction' => 'NestblockWalletToAgent', 'send_amount' => 100.00]
        ];
        
        foreach ($transfers as $transfer) {
            $_SERVER['REQUEST_METHOD'] = 'POST';
            $_POST = [
                'agent_user_id' => $transfer['agent'],
                'direction' => $transfer['direction'],
                'send_amount' => $transfer['send_amount'],
                'send_curr' => 'USD',
                'reason' => 'Multi-agent reconciliation test'
            ];
            
            ob_start();
            try {
                $controller->transfer();
            } catch (Exception $e) {
                // Expected due to redirect
            }
            ob_end_clean();
        }
        
        // Run reconciliation
        require_once __DIR__ . '/../../src/controllers/ReportsController.php';
        $reportsController = new ReportsController();
        $reconciliation = $reportsController->performReconciliation($pdo, $config);
        
        // Verify all agents are reconciled
        $this->assertArrayHasKey($this->agentId, $reconciliation['agent_balances']);
        $this->assertArrayHasKey($agent2Id, $reconciliation['agent_balances']);
        $this->assertArrayHasKey($agent3Id, $reconciliation['agent_balances']);
        
        // All should match
        foreach ([$this->agentId, $agent2Id, $agent3Id] as $agentId) {
            $this->assertEquals('match', $reconciliation['agent_balances'][$agentId]['status'], 
                "Agent $agentId balance should match");
        }
        
        // Should have no discrepancies
        $this->assertEmpty($reconciliation['discrepancies'], 'Should have no discrepancies with multiple agents');
        $this->assertEquals(0, $reconciliation['summary']['failed_checks'], 'Should have no failed checks');
    }
}
