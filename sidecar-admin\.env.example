APP_NAME="Sidecar Admin"
APP_ENV=local
APP_URL=http://localhost:8080
SESSION_SECRET=change_me_to_random_32_char_string

DB_HOST=127.0.0.1
DB_PORT=3306
DB_NAME=vendor_db
DB_USER=sidecar_ops
DB_PASS=change_me

# Vendor table/column names (override if different)
VENDOR_TABLE_USERS=users
VENDOR_TABLE_REMITTANCES=remittances
VENDOR_TABLE_BENEFICIARIES=beneficiaries
VENDOR_TABLE_TRANSACTIONS=transactions
VENDOR_COL_USER_ID=id
VENDOR_COL_USER_BALANCE=balance
VENDOR_COL_REMITTANCE_ID=id
VENDOR_COL_REMITTANCE_STATUS=status
VENDOR_COL_REMITTANCE_BENEFICIARY_ID=beneficiary_id
VENDOR_COL_BENEFICIARY_ID=id
VENDOR_COL_BENEFICIARY_NAME=name
VENDOR_COL_BENEFICIARY_PHONE=phone

# Business rules
REMITTANCE_DISALLOWED_STATUSES=PaidOut,Cancelled

# Currency
DEFAULT_CURRENCY=USD

# Nestblock system user IDs (in vendor users table)
NESTBLOCK_CASH_USER_ID=1001
NESTBLOCK_WALLET_USER_ID=1002

# Transactions
TRX_PREFIX=SC
SET_FINAL_BALANCE_IN_VENDOR_TRANSACTIONS=true

# Security
CSRF_TOKEN_NAME=_token
SESSION_LIFETIME=3600
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# Logging
LOG_LEVEL=info
LOG_FILE=logs/sidecar.log
