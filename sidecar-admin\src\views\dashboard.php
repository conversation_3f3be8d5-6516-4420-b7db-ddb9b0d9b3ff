<?php
$title = 'Dashboard - Sidecar Admin';
$user = getCurrentUser();

// Get some basic stats
global $pdo;

try {
    // Count recent beneficiary amendments
    $recentAmendments = fetchOne($pdo, 
        'SELECT COUNT(*) as count FROM sidecar_beneficiary_amend_audits 
         WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)'
    )['count'] ?? 0;
    
    // Count recent transfers
    $recentTransfers = fetchOne($pdo, 
        'SELECT COUNT(*) as count FROM sidecar_vendor_sync_audits 
         WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)'
    )['count'] ?? 0;
    
    // Get system account balances
    $nestblockCashBalance = fetchOne($pdo, 
        'SELECT balance FROM users WHERE id = ?', 
        [$GLOBALS['config']['business']['nestblock_cash_user_id']]
    )['balance'] ?? 0;
    
    $nestblockWalletBalance = fetchOne($pdo, 
        'SELECT balance FROM users WHERE id = ?', 
        [$GLOBALS['config']['business']['nestblock_wallet_user_id']]
    )['balance'] ?? 0;
    
} catch (Exception $e) {
    logMessage('error', 'Dashboard stats error: ' . $e->getMessage());
    $recentAmendments = $recentTransfers = 0;
    $nestblockCashBalance = $nestblockWalletBalance = 0;
}

ob_start();
?>

<div class="row mt-4">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-speedometer2"></i> Dashboard
            <small class="text-muted">Welcome back, <?= htmlspecialchars($user['email']) ?></small>
        </h1>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= number_format($recentAmendments) ?></h4>
                        <p class="mb-0">Amendments (7d)</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-person-lines-fill fs-2"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= number_format($recentTransfers) ?></h4>
                        <p class="mb-0">Transfers (7d)</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-arrow-left-right fs-2"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= formatCurrency($nestblockCashBalance) ?></h4>
                        <p class="mb-0">NestblockCash</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-cash-stack fs-2"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= formatCurrency($nestblockWalletBalance) ?></h4>
                        <p class="mb-0">NestblockWallet</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-wallet2 fs-2"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php if (hasRole(['Admin', 'Compliance'])): ?>
                    <div class="col-md-4 mb-3">
                        <div class="d-grid">
                            <a href="<?= baseUrl('beneficiaries/amend') ?>" class="btn btn-outline-primary">
                                <i class="bi bi-person-lines-fill"></i>
                                Amend Beneficiary
                            </a>
                        </div>
                        <small class="text-muted">Update beneficiary name and phone</small>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (hasRole(['Admin', 'Finance'])): ?>
                    <div class="col-md-4 mb-3">
                        <div class="d-grid">
                            <a href="<?= baseUrl('agents/transfer') ?>" class="btn btn-outline-success disabled">
                                <i class="bi bi-arrow-left-right"></i>
                                Agent Transfer
                            </a>
                        </div>
                        <small class="text-muted">Transfer funds between accounts</small>
                    </div>
                    <?php endif; ?>
                    
                    <div class="col-md-4 mb-3">
                        <div class="d-grid">
                            <a href="<?= baseUrl('reports/audits') ?>" class="btn btn-outline-info disabled">
                                <i class="bi bi-file-text"></i>
                                View Audit Trail
                            </a>
                        </div>
                        <small class="text-muted">Review system activity logs</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-clock-history"></i> Recent Activity
                </h5>
                <a href="<?= baseUrl('reports/audits') ?>" class="btn btn-sm btn-outline-primary">
                    View All <i class="bi bi-arrow-right"></i>
                </a>
            </div>
            <div class="card-body">
                <?php
                try {
                    // Get recent activity from available audit tables
                    $driver = $pdo->getAttribute(PDO::ATTR_DRIVER_NAME);
                    $beneficiaryTableExists = false;
                    $vendorSyncTableExists = false;

                    if ($driver === 'mysql') {
                        $beneficiaryTableExists = $pdo->query("SHOW TABLES LIKE 'sidecar_beneficiary_amend_audits'")->fetch() !== false;
                        $vendorSyncTableExists = $pdo->query("SHOW TABLES LIKE 'sidecar_vendor_sync_audits'")->fetch() !== false;
                    } else {
                        $beneficiaryTableExists = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='sidecar_beneficiary_amend_audits'")->fetch() !== false;
                        $vendorSyncTableExists = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='sidecar_vendor_sync_audits'")->fetch() !== false;
                    }

                    $queries = [];

                    if ($beneficiaryTableExists) {
                        if ($driver === 'mysql') {
                            $queries[] = "
                                SELECT 'amendment' as type, tx_id, created_at,
                                       CONCAT('Beneficiary amended for invoice ', COALESCE(invoice, 'N/A')) as description,
                                       (SELECT email FROM sidecar_users WHERE id = changed_by_user_id) as user_email
                                FROM sidecar_beneficiary_amend_audits";
                        } else {
                            $queries[] = "
                                SELECT 'amendment' as type, tx_id, created_at,
                                       'Beneficiary amended for invoice ' || COALESCE(invoice, 'N/A') as description,
                                       (SELECT email FROM sidecar_users WHERE id = changed_by_user_id) as user_email
                                FROM sidecar_beneficiary_amend_audits";
                        }
                    }

                    if ($vendorSyncTableExists) {
                        if ($driver === 'mysql') {
                            $queries[] = "
                                SELECT 'transfer' as type, tx_id, created_at,
                                       CONCAT('Transfer: ', counterparty_type, ' ↔ Agent (', send_curr, ' ', ABS(agent_delta), ')') as description,
                                       (SELECT email FROM sidecar_users WHERE id = created_by_user_id) as user_email
                                FROM sidecar_vendor_sync_audits";
                        } else {
                            $queries[] = "
                                SELECT 'transfer' as type, tx_id, created_at,
                                       'Transfer: ' || counterparty_type || ' ↔ Agent (' || send_curr || ' ' || ABS(agent_delta) || ')' as description,
                                       (SELECT email FROM sidecar_users WHERE id = created_by_user_id) as user_email
                                FROM sidecar_vendor_sync_audits";
                        }
                    }

                    if (!empty($queries)) {
                        $sql = implode(' UNION ALL ', $queries) . " ORDER BY created_at DESC LIMIT 10";
                        $recentActivity = fetchAll($pdo, $sql);
                    } else {
                        $recentActivity = [];
                    }
                    
                    if (empty($recentActivity)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-inbox fs-1"></i>
                            <p class="mt-2">No recent activity</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($recentActivity as $activity): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <div class="d-flex align-items-center mb-1">
                                                <?php if ($activity['type'] === 'amendment'): ?>
                                                    <i class="bi bi-person-lines-fill text-primary me-2"></i>
                                                <?php else: ?>
                                                    <i class="bi bi-arrow-left-right text-success me-2"></i>
                                                <?php endif; ?>
                                                <strong><?= htmlspecialchars($activity['description']) ?></strong>
                                            </div>
                                            <small class="text-muted">
                                                by <?= htmlspecialchars($activity['user_email']) ?> • 
                                                TX: <?= htmlspecialchars($activity['tx_id']) ?>
                                            </small>
                                        </div>
                                        <small class="text-muted">
                                            <?= date('M j, Y g:i A', strtotime($activity['created_at'])) ?>
                                        </small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif;
                } catch (Exception $e) {
                    logMessage('error', 'Recent activity error: ' . $e->getMessage());
                    ?>
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        Unable to load recent activity at this time.
                    </div>
                <?php } ?>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/layout.php';
?>
