<?php
/**
 * Accounting Controller
 * Handles agent transfer operations with double-entry bookkeeping
 */

class AccountingController {
    
    /**
     * Handle agent transfer request
     */
    public function transfer($params = []) {
        requireRole(['Admin', 'Finance']);
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(baseUrl('agents/transfer'));
        }
        
        global $pdo, $config;
        
        // Get and validate input
        $agentUserId = (int)($_POST['agent_user_id'] ?? 0);
        $direction = sanitizeInput($_POST['direction'] ?? '');
        $send_amount = (float)($_POST['send_amount'] ?? 0);
        $send_curr = sanitizeInput($_POST['send_curr'] ?? $config['business']['default_send_curr']);
        $reason = sanitizeInput($_POST['reason'] ?? '');
        
        try {
            // Validate input
            $this->validateTransferInput($agentUserId, $direction, $send_amount, $send_curr, $reason);
            
            // Execute transfer in transaction
            $result = executeInTransaction($pdo, function($pdo) use ($agentUserId, $direction, $send_amount, $send_curr, $reason, $config) {
                return $this->performTransfer($pdo, $agentUserId, $direction, $send_amount, $send_curr, $reason, $config);
            });
            
            // Success response
            setFlashMessage('success', 'Transfer completed successfully');
            
            // Redirect to result page with transaction ID
            redirect(baseUrl('agents/transfer/result?tx_id=' . $result['tx_id']));
            
        } catch (Exception $e) {
            logMessage('error', 'Agent transfer error: ' . $e->getMessage(), [
                'agent_user_id' => $agentUserId,
                'direction' => $direction,
                'send_amount' => $send_amount,
                'send_curr' => $send_curr,
                'user_id' => getCurrentUser()['id']
            ]);
            
            setFlashMessage('error', $e->getMessage());
            redirect(baseUrl('agents/transfer'));
        }
    }
    
    /**
     * Validate transfer input
     */
    private function validateTransferInput($agentUserId, $direction, $send_amount, $send_curr, $reason) {
        if (!$agentUserId) {
            throw new InvalidArgumentException('Agent user ID is required');
        }
        
        if (!isValidTransferDirection($direction)) {
            throw new InvalidArgumentException('Invalid transfer direction');
        }
        
        if (!isValidsend_amount($send_amount)) {
            throw new InvalidArgumentException('Invalid send_amount. Must be a positive number with up to 2 decimal places');
        }
        
        if (empty($send_curr) || strlen($send_curr) !== 3) {
            throw new InvalidArgumentException('Invalid send_curr code');
        }
        
        if (empty($reason) || strlen($reason) < 5) {
            throw new InvalidArgumentException('Reason is required and must be at least 5 characters');
        }
    }
    
    /**
     * Perform the actual transfer operation
     */
    private function performTransfer($pdo, $agentUserId, $direction, $send_amount, $send_curr, $reason, $config) {
        $user = getCurrentUser();
        
        // Get counterparty information
        $counterparty = getCounterpartyInfo($direction);
        $counterpartyUserId = $counterparty['user_id'];
        $counterpartyType = $counterparty['type'];
        
        // Generate transaction ID
        $txId = generateUuid();
        
        // Check for idempotency
        $existingSync = fetchOne($pdo, 
            'SELECT tx_id FROM sidecar_vendor_sync_audits WHERE tx_id = ?', 
            [$txId]
        );
        
        if ($existingSync) {
            throw new InvalidArgumentException('Transaction already processed');
        }
        
        // Lock users for update and get current balances
        $userBalances = lockUsersForUpdate($pdo, [$agentUserId, $counterpartyUserId]);
        
        if (!isset($userBalances[$agentUserId])) {
            throw new InvalidArgumentException('Agent user not found');
        }
        
        if (!isset($userBalances[$counterpartyUserId])) {
            throw new InvalidArgumentException('Counterparty user not found');
        }
        
        $agentBalanceBefore = $userBalances[$agentUserId];
        $counterpartyBalanceBefore = $userBalances[$counterpartyUserId];
        
        // Calculate balance changes
        $balanceChanges = getVendorBalanceChanges($direction, $send_amount);
        $agentDelta = $balanceChanges['agent_delta'];
        $counterpartyDelta = $balanceChanges['counterparty_delta'];
        
        $agentBalanceAfter = $agentBalanceBefore + $agentDelta;
        $counterpartyBalanceAfter = $counterpartyBalanceBefore + $counterpartyDelta;
        
        // Check for sufficient funds
        if ($agentBalanceAfter < -0.01) { // Allow small floating point errors
            throw new InvalidArgumentException('Insufficient agent balance');
        }
        
        if ($counterpartyBalanceAfter < -0.01) {
            throw new InvalidArgumentException('Insufficient counterparty balance');
        }
        
        // Ensure or create sidecar accounts
        $agentAccountId = $this->ensureSidecarAccount($pdo, $agentUserId, 'Agent', $send_curr);
        $counterpartyAccountId = $this->ensureSidecarAccount($pdo, $counterpartyUserId, $counterpartyType, $send_curr);
        
        // Create sidecar ledger entries
        $this->createLedgerEntries($pdo, $txId, $direction, $agentAccountId, $counterpartyAccountId, $send_amount, $user, $reason, $send_curr);
        
        // Update vendor user balances
        updateUserBalance($pdo, $agentUserId, $agentBalanceAfter);
        updateUserBalance($pdo, $counterpartyUserId, $counterpartyBalanceAfter);
        
        // Insert vendor transaction records
        $this->insertVendorTransactions($pdo, $txId, $agentUserId, $counterpartyUserId, $agentDelta, $counterpartyDelta, $agentBalanceAfter, $counterpartyBalanceAfter, $direction, $config);
        
        // Create sync audit record
        insertRecord($pdo, 'sidecar_vendor_sync_audits', [
            'tx_id' => $txId,
            'agent_user_id' => $agentUserId,
            'counterparty_user_id' => $counterpartyUserId,
            'counterparty_type' => $counterpartyType,
            'send_curr' => $send_curr,
            'agent_delta' => $agentDelta,
            'counterparty_delta' => $counterpartyDelta,
            'agent_before' => $agentBalanceBefore,
            'agent_after' => $agentBalanceAfter,
            'counterparty_before' => $counterpartyBalanceBefore,
            'counterparty_after' => $counterpartyBalanceAfter,
            'created_by_user_id' => $user['id'],
            'reason' => $reason
        ]);
        
        // Log successful transfer
        logMessage('info', 'Agent transfer completed successfully', [
            'tx_id' => $txId,
            'agent_user_id' => $agentUserId,
            'counterparty_user_id' => $counterpartyUserId,
            'direction' => $direction,
            'send_amount' => $send_amount,
            'send_curr' => $send_curr,
            'user_id' => $user['id']
        ]);
        
        return [
            'tx_id' => $txId,
            'direction' => $direction,
            'send_amount' => $send_amount,
            'send_curr' => $send_curr,
            'agent_user_id' => $agentUserId,
            'counterparty_user_id' => $counterpartyUserId,
            'counterparty_type' => $counterpartyType,
            'agent_balance_before' => $agentBalanceBefore,
            'agent_balance_after' => $agentBalanceAfter,
            'counterparty_balance_before' => $counterpartyBalanceBefore,
            'counterparty_balance_after' => $counterpartyBalanceAfter,
            'reason' => $reason,
            'created_by' => $user['email'],
            'created_at' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Ensure sidecar account exists
     */
    private function ensureSidecarAccount($pdo, $ownerUserId, $accountType, $send_curr) {
        // Try to find existing account
        $account = fetchOne($pdo, 
            'SELECT id FROM sidecar_accounts WHERE owner_user_id = ? AND account_type = ? AND send_curr = ?',
            [$ownerUserId, $accountType, $send_curr]
        );
        
        if ($account) {
            return $account['id'];
        }
        
        // Create new account
        return insertRecord($pdo, 'sidecar_accounts', [
            'owner_user_id' => $ownerUserId,
            'account_type' => $accountType,
            'send_curr' => $send_curr
        ]);
    }
    
    /**
     * Create sidecar ledger entries
     */
    private function createLedgerEntries($pdo, $txId, $direction, $agentAccountId, $counterpartyAccountId, $send_amount, $user, $reason, $send_curr) {
        $postingRules = getPostingRules($direction);
        
        $meta = [
            'by' => $user['id'],
            'reason' => $reason,
            'direction' => $direction,
            'send_curr' => $send_curr
        ];
        
        // Determine which account gets debited and which gets credited
        if ($postingRules['debit'] === 'Agent') {
            $debitAccountId = $agentAccountId;
            $creditAccountId = $counterpartyAccountId;
        } else {
            $debitAccountId = $counterpartyAccountId;
            $creditAccountId = $agentAccountId;
        }
        
        // Insert debit entry
        insertRecord($pdo, 'sidecar_ledger_entries', [
            'tx_id' => $txId,
            'account_id' => $debitAccountId,
            'direction' => 'Debit',
            'send_amount' => $send_amount,
            'meta' => json_encode($meta)
        ]);
        
        // Insert credit entry
        insertRecord($pdo, 'sidecar_ledger_entries', [
            'tx_id' => $txId,
            'account_id' => $creditAccountId,
            'direction' => 'Credit',
            'send_amount' => $send_amount,
            'meta' => json_encode($meta)
        ]);
    }
    
    /**
     * Insert vendor transaction records
     */
    private function insertVendorTransactions($pdo, $txId, $agentUserId, $counterpartyUserId, $agentDelta, $counterpartyDelta, $agentBalanceAfter, $counterpartyBalanceAfter, $direction, $config) {
        $transactionTable = getTableName('transactions');
        
        // Generate vendor transaction IDs
        $agentTrxId = generateVendorTrxId($txId, 'AGENT');
        $counterpartyTrxId = generateVendorTrxId($txId, 'NB');
        
        $remarks = "SIDECAR $direction";
        
        // Insert agent transaction
        $this->insertVendorTransaction($pdo, $agentUserId, abs($agentDelta), 
            $agentDelta > 0 ? '+' : '-', $remarks, $agentTrxId, 
            $config['business']['set_final_balance_in_vendor_transactions'] ? $agentBalanceAfter : null);
        
        // Insert counterparty transaction
        $this->insertVendorTransaction($pdo, $counterpartyUserId, abs($counterpartyDelta), 
            $counterpartyDelta > 0 ? '+' : '-', $remarks, $counterpartyTrxId, 
            $config['business']['set_final_balance_in_vendor_transactions'] ? $counterpartyBalanceAfter : null);
    }
    
    /**
     * Insert single vendor transaction with idempotency check
     */
    private function insertVendorTransaction($pdo, $userId, $send_amount, $trxType, $remarks, $trxId, $finalBalance) {
        $transactionTable = getTableName('transactions');
        
        // Check for idempotency
        $existing = fetchOne($pdo, 
            "SELECT 1 FROM $transactionTable WHERE trx_id = ? LIMIT 1", 
            [$trxId]
        );
        
        if ($existing) {
            return; // Already exists, skip
        }
        
        // Insert transaction
        $sql = "INSERT INTO $transactionTable 
                (user_id, send_amount, charge, final_balance, trx_type, remarks, trx_id, created_at, updated_at)
                VALUES (?, ?, 0, ?, ?, ?, ?, NOW(), NOW())";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $userId,
            $send_amount,
            $finalBalance !== null ? number_format($finalBalance, 2, '.', '') : null,
            $trxType,
            $remarks,
            $trxId
        ]);
    }
}
