<?php
/**
 * Fix production audit tables
 * This script will create missing tables and fix column issues
 */

require_once 'src/bootstrap.php';

echo "=== FIXING PRODUCTION AUDIT TABLES ===\n\n";

try {
    $driver = $pdo->getAttribute(PDO::ATTR_DRIVER_NAME);
    echo "Database Driver: $driver\n\n";
    
    if ($driver !== 'mysql') {
        echo "This script is designed for MySQL databases.\n";
        echo "For other databases, please adapt the SQL accordingly.\n";
        exit(1);
    }
    
    // Check current table status
    echo "1. Checking current table status...\n";
    
    $beneficiaryExists = $pdo->query("SHOW TABLES LIKE 'sidecar_beneficiary_amend_audits'")->fetch() !== false;
    $vendorSyncExists = $pdo->query("SHOW TABLES LIKE 'sidecar_vendor_sync_audits'")->fetch() !== false;
    
    echo "   - sidecar_beneficiary_amend_audits: " . ($beneficiaryExists ? "EXISTS" : "MISSING") . "\n";
    echo "   - sidecar_vendor_sync_audits: " . ($vendorSyncExists ? "EXISTS" : "MISSING") . "\n\n";
    
    // Create sidecar_vendor_sync_audits if missing
    if (!$vendorSyncExists) {
        echo "2. Creating sidecar_vendor_sync_audits table...\n";
        
        $pdo->exec("
            CREATE TABLE sidecar_vendor_sync_audits (
              id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
              tx_id CHAR(36) NOT NULL,
              agent_user_id BIGINT UNSIGNED NOT NULL,
              counterparty_user_id BIGINT UNSIGNED NOT NULL,
              counterparty_type VARCHAR(50) NOT NULL,
              send_curr CHAR(3) NOT NULL DEFAULT 'USD',
              agent_delta DECIMAL(18,2) NOT NULL,
              counterparty_delta DECIMAL(18,2) NOT NULL,
              agent_before DECIMAL(18,2) NOT NULL,
              agent_after DECIMAL(18,2) NOT NULL,
              counterparty_before DECIMAL(18,2) NOT NULL,
              counterparty_after DECIMAL(18,2) NOT NULL,
              created_by_user_id BIGINT UNSIGNED NOT NULL,
              reason TEXT NOT NULL,
              created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
              UNIQUE KEY uniq_sync_tx (tx_id),
              INDEX idx_agent (agent_user_id),
              INDEX idx_counterparty (counterparty_user_id),
              INDEX idx_created_at (created_at)
            )
        ");
        
        echo "   ✅ Created sidecar_vendor_sync_audits table\n\n";
    } else {
        echo "2. sidecar_vendor_sync_audits table already exists\n\n";
    }
    
    // Check and fix beneficiary audit table columns
    if ($beneficiaryExists) {
        echo "3. Checking sidecar_beneficiary_amend_audits columns...\n";
        
        $columns = $pdo->query("DESCRIBE sidecar_beneficiary_amend_audits")->fetchAll(PDO::FETCH_COLUMN);
        
        $hasInvoice = in_array('invoice', $columns);
        $hasOldEmail = in_array('old_email', $columns);
        $hasNewEmail = in_array('new_email', $columns);
        
        echo "   - invoice column: " . ($hasInvoice ? "EXISTS" : "MISSING") . "\n";
        echo "   - old_email column: " . ($hasOldEmail ? "EXISTS" : "MISSING") . "\n";
        echo "   - new_email column: " . ($hasNewEmail ? "EXISTS" : "MISSING") . "\n";
        
        // Add missing columns
        if (!$hasInvoice) {
            echo "   Adding invoice column...\n";
            $pdo->exec("ALTER TABLE sidecar_beneficiary_amend_audits ADD COLUMN invoice VARCHAR(100) AFTER tx_id");
            echo "   ✅ Added invoice column\n";
        }
        
        if (!$hasOldEmail) {
            echo "   Adding old_email column...\n";
            $pdo->exec("ALTER TABLE sidecar_beneficiary_amend_audits ADD COLUMN old_email VARCHAR(255) AFTER new_name");
            echo "   ✅ Added old_email column\n";
        }
        
        if (!$hasNewEmail) {
            echo "   Adding new_email column...\n";
            $pdo->exec("ALTER TABLE sidecar_beneficiary_amend_audits ADD COLUMN new_email VARCHAR(255) AFTER old_email");
            echo "   ✅ Added new_email column\n";
        }
        
        echo "\n";
    }
    
    // Test the audit queries
    echo "4. Testing audit queries...\n";
    
    try {
        $testQuery = "
            SELECT 'amendment' as type, tx_id, created_at,
                   CONCAT('Beneficiary amended for invoice ', COALESCE(invoice, 'N/A')) as description,
                   (SELECT email FROM sidecar_users WHERE id = changed_by_user_id) as user_email
            FROM sidecar_beneficiary_amend_audits
            LIMIT 1
        ";
        $pdo->query($testQuery);
        echo "   ✅ Beneficiary audit query works\n";
    } catch (Exception $e) {
        echo "   ❌ Beneficiary audit query failed: " . $e->getMessage() . "\n";
    }
    
    try {
        $testQuery2 = "
            SELECT 'transfer' as type, tx_id, created_at,
                   CONCAT('Transfer: ', counterparty_type, ' ↔ Agent (', send_curr, ' ', ABS(agent_delta), ')') as description,
                   (SELECT email FROM sidecar_users WHERE id = created_by_user_id) as user_email
            FROM sidecar_vendor_sync_audits
            LIMIT 1
        ";
        $pdo->query($testQuery2);
        echo "   ✅ Vendor sync audit query works\n";
    } catch (Exception $e) {
        echo "   ❌ Vendor sync audit query failed: " . $e->getMessage() . "\n";
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "✅ PRODUCTION AUDIT TABLES FIXED!\n";
    echo str_repeat("=", 50) . "\n";
    
    echo "\nThe audit reports should now work at:\n";
    echo "- /reports/audits\n";
    echo "- Dashboard recent activity\n\n";
    
    echo "Final table status:\n";
    $finalBeneficiaryExists = $pdo->query("SHOW TABLES LIKE 'sidecar_beneficiary_amend_audits'")->fetch() !== false;
    $finalVendorSyncExists = $pdo->query("SHOW TABLES LIKE 'sidecar_vendor_sync_audits'")->fetch() !== false;
    
    echo "- sidecar_beneficiary_amend_audits: " . ($finalBeneficiaryExists ? "✅ EXISTS" : "❌ MISSING") . "\n";
    echo "- sidecar_vendor_sync_audits: " . ($finalVendorSyncExists ? "✅ EXISTS" : "❌ MISSING") . "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
