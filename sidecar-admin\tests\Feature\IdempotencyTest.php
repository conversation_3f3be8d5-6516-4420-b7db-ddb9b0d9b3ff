<?php

use PHPUnit\Framework\TestCase;

class IdempotencyTest extends TestCase {
    
    private $userId;
    private $agentId;
    private $beneficiaryId;
    private $remittanceId;
    
    protected function setUp(): void {
        // Clean up
        cleanupTestData();
        
        // Create test user
        $this->userId = createTestUser('<EMAIL>', 'Admin');
        
        // Create test data
        $this->agentId = createTestAgent(9002, 2000.00);
        $this->beneficiaryId = createTestBeneficiary('Test Beneficiary', '+**********');
        $this->remittanceId = createTestRemittance($this->beneficiaryId, 'Pending');
        
        // Login test user
        loginTestUser($this->userId);
    }
    
    protected function tearDown(): void {
        cleanupTestData();
    }
    
    public function testBeneficiaryAmendmentIdempotency() {
        global $pdo;
        
        require_once __DIR__ . '/../../src/controllers/BeneficiaryController.php';
        $controller = new BeneficiaryController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'remittance_id' => $this->remittanceId,
            'name' => 'Updated Name',
            'phone' => '+**********',
            'reason' => 'Testing idempotency for beneficiary amendment'
        ];
        
        // First amendment
        ob_start();
        try {
            $controller->amend();
        } catch (Exception $e) {
            // Expected due to redirect
        }
        ob_end_clean();
        
        // Get the first audit record
        $firstAudit = fetchOne($pdo, 
            'SELECT * FROM sidecar_beneficiary_amend_audits WHERE remittance_id = ?',
            [$this->remittanceId]
        );
        
        $this->assertNotNull($firstAudit);
        
        // Reset beneficiary to original state for second attempt
        updateRecord($pdo, 'beneficiaries', 
            ['name' => 'Test Beneficiary', 'phone' => '+**********'], 
            'id = ?', 
            [$this->beneficiaryId]
        );
        
        // Second amendment with same data
        ob_start();
        try {
            $controller->amend();
        } catch (Exception $e) {
            // Expected due to redirect
        }
        ob_end_clean();
        
        // Verify only one audit record exists (idempotent)
        $auditCount = fetchOne($pdo, 
            'SELECT COUNT(*) as count FROM sidecar_beneficiary_amend_audits WHERE remittance_id = ?',
            [$this->remittanceId]
        )['count'];
        
        $this->assertEquals(2, $auditCount, 'Should have 2 audit records for 2 separate amendments');
        
        // Verify each has unique tx_id
        $txIds = fetchAll($pdo, 
            'SELECT DISTINCT tx_id FROM sidecar_beneficiary_amend_audits WHERE remittance_id = ?',
            [$this->remittanceId]
        );
        
        $this->assertCount(2, $txIds, 'Each amendment should have unique transaction ID');
    }
    
    public function testAgentTransferIdempotency() {
        global $pdo;
        
        $send_amount = 500.00;
        $initialBalance = getVendorBalance($this->agentId);
        
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'agent_user_id' => $this->agentId,
            'direction' => 'AgentToNestblockCash',
            'send_amount' => $send_amount,
            'send_curr' => 'USD',
            'reason' => 'Testing idempotency for agent transfer'
        ];
        
        // First transfer
        ob_start();
        try {
            $controller->transfer();
        } catch (Exception $e) {
            // Expected due to redirect
        }
        ob_end_clean();
        
        $balanceAfterFirst = getVendorBalance($this->agentId);
        $expectedBalance = $initialBalance - $send_amount;
        
        assertBalanceEquals($expectedBalance, $balanceAfterFirst, 'First transfer should update balance');
        
        // Get first sync audit
        $firstSyncAudit = fetchOne($pdo, 
            'SELECT * FROM sidecar_vendor_sync_audits WHERE agent_user_id = ? ORDER BY created_at DESC LIMIT 1',
            [$this->agentId]
        );
        
        $this->assertNotNull($firstSyncAudit);
        
        // Second transfer with same parameters (should create new transaction)
        ob_start();
        try {
            $controller->transfer();
        } catch (Exception $e) {
            // Expected due to redirect
        }
        ob_end_clean();
        
        $balanceAfterSecond = getVendorBalance($this->agentId);
        $expectedBalanceAfterSecond = $expectedBalance - $send_amount;
        
        assertBalanceEquals($expectedBalanceAfterSecond, $balanceAfterSecond, 
            'Second transfer should further update balance');
        
        // Verify two separate sync audits exist
        $syncAuditCount = fetchOne($pdo, 
            'SELECT COUNT(*) as count FROM sidecar_vendor_sync_audits WHERE agent_user_id = ?',
            [$this->agentId]
        )['count'];
        
        $this->assertEquals(2, $syncAuditCount, 'Should have 2 separate transfer records');
        
        // Verify unique transaction IDs
        $txIds = fetchAll($pdo, 
            'SELECT DISTINCT tx_id FROM sidecar_vendor_sync_audits WHERE agent_user_id = ?',
            [$this->agentId]
        );
        
        $this->assertCount(2, $txIds, 'Each transfer should have unique transaction ID');
    }
    
    public function testTransactionIdUniqueness() {
        global $pdo;
        
        // Perform multiple operations to test UUID uniqueness
        require_once __DIR__ . '/../../src/controllers/BeneficiaryController.php';
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        
        $beneficiaryController = new BeneficiaryController();
        $accountingController = new AccountingController();
        
        $allTxIds = [];
        
        // Perform 5 beneficiary amendments
        for ($i = 1; $i <= 5; $i++) {
            // Reset beneficiary
            updateRecord($pdo, 'beneficiaries', 
                ['name' => "Original Name $i", 'phone' => '+**********'], 
                'id = ?', 
                [$this->beneficiaryId]
            );
            
            $_SERVER['REQUEST_METHOD'] = 'POST';
            $_POST = [
                'remittance_id' => $this->remittanceId,
                'name' => "Updated Name $i",
                'phone' => '+**********',
                'reason' => "Amendment number $i for uniqueness test"
            ];
            
            ob_start();
            try {
                $beneficiaryController->amend();
            } catch (Exception $e) {
                // Expected due to redirect
            }
            ob_end_clean();
        }
        
        // Perform 5 agent transfers
        for ($i = 1; $i <= 5; $i++) {
            $_SERVER['REQUEST_METHOD'] = 'POST';
            $_POST = [
                'agent_user_id' => $this->agentId,
                'direction' => 'AgentToNestblockCash',
                'send_amount' => 50.00,
                'send_curr' => 'USD',
                'reason' => "Transfer number $i for uniqueness test"
            ];
            
            ob_start();
            try {
                $accountingController->transfer();
            } catch (Exception $e) {
                // Expected due to redirect
            }
            ob_end_clean();
        }
        
        // Collect all transaction IDs
        $beneficiaryTxIds = fetchAll($pdo, 
            'SELECT tx_id FROM sidecar_beneficiary_amend_audits WHERE remittance_id = ?',
            [$this->remittanceId]
        );
        
        $transferTxIds = fetchAll($pdo, 
            'SELECT tx_id FROM sidecar_vendor_sync_audits WHERE agent_user_id = ?',
            [$this->agentId]
        );
        
        foreach ($beneficiaryTxIds as $row) {
            $allTxIds[] = $row['tx_id'];
        }
        
        foreach ($transferTxIds as $row) {
            $allTxIds[] = $row['tx_id'];
        }
        
        // Verify all transaction IDs are unique
        $uniqueTxIds = array_unique($allTxIds);
        $this->assertCount(count($allTxIds), $uniqueTxIds, 'All transaction IDs should be unique');
        
        // Verify UUID format (36 characters with hyphens)
        foreach ($allTxIds as $txId) {
            $this->assertEquals(36, strlen($txId), 'Transaction ID should be 36 characters (UUID format)');
            $this->assertMatchesRegularExpression(
                '/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i',
                $txId,
                'Transaction ID should match UUID format'
            );
        }
    }
    
    public function testConcurrentOperationSafety() {
        global $pdo;
        
        // This test simulates concurrent operations by checking database constraints
        // In a real concurrent scenario, database constraints would prevent duplicates
        
        require_once __DIR__ . '/../../src/controllers/AccountingController.php';
        $controller = new AccountingController();
        
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = [
            'agent_user_id' => $this->agentId,
            'direction' => 'AgentToNestblockCash',
            'send_amount' => 100.00,
            'send_curr' => 'USD',
            'reason' => 'Testing concurrent operation safety'
        ];
        
        // Perform transfer
        ob_start();
        try {
            $controller->transfer();
        } catch (Exception $e) {
            // Expected due to redirect
        }
        ob_end_clean();
        
        // Get the transaction ID
        $syncAudit = fetchOne($pdo, 
            'SELECT tx_id FROM sidecar_vendor_sync_audits WHERE agent_user_id = ? ORDER BY created_at DESC LIMIT 1',
            [$this->agentId]
        );
        
        $txId = $syncAudit['tx_id'];
        
        // Verify database constraints would prevent duplicate tx_id
        try {
            // Attempt to insert duplicate audit record with same tx_id
            insertRecord($pdo, 'sidecar_vendor_sync_audits', [
                'tx_id' => $txId, // Same tx_id
                'agent_user_id' => $this->agentId,
                'counterparty_user_id' => 1001,
                'counterparty_type' => 'NestblockCash',
                'send_curr' => 'USD',
                'agent_delta' => -100.00,
                'counterparty_delta' => 100.00,
                'agent_before' => 2000.00,
                'agent_after' => 1900.00,
                'counterparty_before' => 0.00,
                'counterparty_after' => 100.00,
                'reason' => 'Duplicate test',
                'created_by_user_id' => $this->userId
            ]);
            
            $this->fail('Should not be able to insert duplicate tx_id');
            
        } catch (PDOException $e) {
            // Expected - unique constraint violation
            $this->assertStringContainsString('Duplicate entry', $e->getMessage());
        }
    }
}
